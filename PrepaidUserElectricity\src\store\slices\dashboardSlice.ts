import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { DashboardState } from '../../types';
import { getCurrentUnits, getThresholdLimit, getAppConfig } from '../../storage/asyncStorage';
import { usageService, purchaseService } from '../../services/platformDatabaseService';

// Initial state
const initialState: DashboardState = {
  currentUnits: 0,
  usageSinceLastRecording: 0,
  weeklyTotal: {
    usage: 0,
    currency: 0,
  },
  monthlyTotal: {
    usage: 0,
    currency: 0,
  },
  isLowUnitsWarning: false,
  lastRecordingDate: new Date().toISOString(),
};

// Async thunks
export const loadDashboardData = createAsyncThunk(
  'dashboard/loadData',
  async () => {
    try {
      const config = await getAppConfig();
      const currentUnits = await getCurrentUnits();
      const thresholdLimit = await getThresholdLimit();
      
      // Calculate usage since last recording
      const usageSinceLastRecording = await usageService.calculateUsageSinceLastRecording(currentUnits);
      
      // Get weekly totals
      const now = new Date();
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - 7);
      
      const weeklyPurchases = await purchaseService.getTotalPurchases(
        weekStart.toISOString(),
        now.toISOString()
      );
      const weeklyUsage = await usageService.getTotalUsage(
        weekStart.toISOString(),
        now.toISOString()
      );
      
      // Get monthly totals
      const monthStart = new Date(now);
      monthStart.setMonth(now.getMonth() - 1);
      
      const monthlyPurchases = await purchaseService.getTotalPurchases(
        monthStart.toISOString(),
        now.toISOString()
      );
      const monthlyUsage = await usageService.getTotalUsage(
        monthStart.toISOString(),
        now.toISOString()
      );
      
      // Get last recording date
      const latestUsage = await usageService.getLatestUsageRecord();
      const lastRecordingDate = latestUsage?.record_date || new Date().toISOString();
      
      return {
        currentUnits,
        usageSinceLastRecording,
        weeklyTotal: {
          usage: weeklyUsage,
          currency: weeklyPurchases.currency,
        },
        monthlyTotal: {
          usage: monthlyUsage,
          currency: monthlyPurchases.currency,
        },
        isLowUnitsWarning: currentUnits <= thresholdLimit,
        lastRecordingDate,
        thresholdLimit,
        selectedCurrency: config.selectedCurrency,
        selectedUnit: config.selectedUnit,
        costPerUnit: config.costPerUnit,
      };
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      throw error;
    }
  }
);

export const updateCurrentUnits = createAsyncThunk(
  'dashboard/updateCurrentUnits',
  async (units: number) => {
    try {
      // This would typically update the database/storage
      return units;
    } catch (error) {
      console.error('Error updating current units:', error);
      throw error;
    }
  }
);

export const refreshDashboard = createAsyncThunk(
  'dashboard/refresh',
  async (_, { dispatch }) => {
    return dispatch(loadDashboardData());
  }
);

// Dashboard slice
const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    setCurrentUnits: (state, action: PayloadAction<number>) => {
      state.currentUnits = action.payload;
      // Recalculate low units warning
      // Note: threshold would need to be available in state or passed as payload
    },
    setUsageSinceLastRecording: (state, action: PayloadAction<number>) => {
      state.usageSinceLastRecording = action.payload;
    },
    setLowUnitsWarning: (state, action: PayloadAction<boolean>) => {
      state.isLowUnitsWarning = action.payload;
    },
    updateWeeklyTotal: (state, action: PayloadAction<{ usage: number; currency: number }>) => {
      state.weeklyTotal = action.payload;
    },
    updateMonthlyTotal: (state, action: PayloadAction<{ usage: number; currency: number }>) => {
      state.monthlyTotal = action.payload;
    },
    setLastRecordingDate: (state, action: PayloadAction<string>) => {
      state.lastRecordingDate = action.payload;
    },
    resetDashboard: (state) => {
      return {
        ...initialState,
        lastRecordingDate: new Date().toISOString(),
      };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loadDashboardData.fulfilled, (state, action) => {
        const {
          currentUnits,
          usageSinceLastRecording,
          weeklyTotal,
          monthlyTotal,
          isLowUnitsWarning,
          lastRecordingDate,
        } = action.payload;
        
        state.currentUnits = currentUnits;
        state.usageSinceLastRecording = usageSinceLastRecording;
        state.weeklyTotal = weeklyTotal;
        state.monthlyTotal = monthlyTotal;
        state.isLowUnitsWarning = isLowUnitsWarning;
        state.lastRecordingDate = lastRecordingDate;
      })
      .addCase(loadDashboardData.rejected, (state, action) => {
        console.error('Failed to load dashboard data:', action.error);
      })
      .addCase(updateCurrentUnits.fulfilled, (state, action) => {
        state.currentUnits = action.payload;
      });
  },
});

export const {
  setCurrentUnits,
  setUsageSinceLastRecording,
  setLowUnitsWarning,
  updateWeeklyTotal,
  updateMonthlyTotal,
  setLastRecordingDate,
  resetDashboard,
} = dashboardSlice.actions;

export default dashboardSlice.reducer;
