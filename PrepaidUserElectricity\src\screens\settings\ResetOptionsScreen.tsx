import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAppDispatch, useAppSelector } from '../../store';
import { resetDashboard } from '../../store/slices/dashboardSlice';
import { resetSettings } from '../../store/slices/settingsSlice';
import { useTheme } from '../../context/ThemeContext';
import { purchaseService, usageService, historyService, settingsService } from '../../services/platformDatabaseService';
import { useNavigation } from '@react-navigation/native';

const ResetOptionsScreen = () => {
  const dispatch = useAppDispatch();
  const navigation = useNavigation();
  const { currentTheme } = useTheme();
  const [showFactoryResetModal, setShowFactoryResetModal] = useState(false);
  const [showDashboardResetModal, setShowDashboardResetModal] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');
  const [isResetting, setIsResetting] = useState(false);
  const [dataStats, setDataStats] = useState({
    purchases: 0,
    usage: 0,
    history: 0,
    dbSize: '0 KB',
  });

  useEffect(() => {
    loadDataStats();
  }, []);

  const loadDataStats = async () => {
    try {
      const purchases = await purchaseService.getAllPurchases();
      const usage = await usageService.getAllUsageRecords();
      const history = await historyService.getAllHistory();

      setDataStats({
        purchases: purchases.length,
        usage: usage.length,
        history: history.length,
        dbSize: '2.4 MB', // Placeholder - would need native module to get actual size
      });
    } catch (error) {
      console.error('Error loading data stats:', error);
    }
  };

  const handleFactoryReset = async () => {
    if (confirmationText.toLowerCase() !== 'reset') {
      Alert.alert('Error', 'Please type "RESET" to confirm');
      return;
    }

    try {
      setIsResetting(true);

      // Clear all database tables
      await purchaseService.clearAllPurchases();
      await usageService.clearAllUsageRecords();
      await historyService.clearHistory();
      await settingsService.clearAllSettings();

      // Reset Redux stores
      dispatch(resetDashboard());
      await dispatch(resetSettings('all')).unwrap();

      Alert.alert(
        'Factory Reset Complete',
        'All data has been cleared. The app will restart.',
        [
          {
            text: 'OK',
            onPress: () => {
              setShowFactoryResetModal(false);
              setConfirmationText('');
              // Refresh data stats
              loadDataStats();
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error during factory reset:', error);
      Alert.alert('Error', 'Failed to complete factory reset. Please try again.');
    } finally {
      setIsResetting(false);
    }
  };

  const handleDashboardReset = async () => {
    if (confirmationText.toLowerCase() !== 'reset') {
      Alert.alert('Error', 'Please type "RESET" to confirm');
      return;
    }

    try {
      setIsResetting(true);

      // Clear only dashboard data
      dispatch(resetDashboard());
      await dispatch(resetSettings('dashboard')).unwrap();

      Alert.alert(
        'Dashboard Reset Complete',
        'Dashboard data has been cleared. History remains intact.',
        [
          {
            text: 'OK',
            onPress: () => {
              setShowDashboardResetModal(false);
              setConfirmationText('');
              // Refresh data stats
              loadDataStats();
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error during dashboard reset:', error);
      Alert.alert('Error', 'Failed to complete dashboard reset. Please try again.');
    } finally {
      setIsResetting(false);
    }
  };

  const resetOptions = [
    {
      title: 'Factory Reset',
      subtitle: 'Clear all data and reset app to initial state',
      icon: 'restore',
      color: currentTheme.colors.error,
      description: 'This will permanently delete all your data including purchases, usage records, history, and settings. The app will return to its initial setup state.',
      onPress: () => setShowFactoryResetModal(true),
    },
    {
      title: 'Dashboard Data Reset',
      subtitle: 'Clear dashboard data only, keep history intact',
      icon: 'refresh',
      color: currentTheme.colors.warning,
      description: 'This will clear current units, usage calculations, and dashboard totals. Your purchase and usage history will be preserved.',
      onPress: () => setShowDashboardResetModal(true),
    },
  ];

  const renderResetModal = (
    visible: boolean,
    onClose: () => void,
    onConfirm: () => void,
    title: string,
    description: string,
    color: string
  ) => (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Icon name="warning" size={40} color={color} />
            <Text style={styles.modalTitle}>{title}</Text>
          </View>
          
          <Text style={styles.modalDescription}>{description}</Text>
          
          <View style={styles.confirmationContainer}>
            <Text style={styles.confirmationLabel}>
              Type "RESET" to confirm:
            </Text>
            <TextInput
              style={styles.confirmationInput}
              value={confirmationText}
              onChangeText={setConfirmationText}
              placeholder="Type RESET here"
              placeholderTextColor="#999"
              autoCapitalize="characters"
            />
          </View>
          
          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => {
                onClose();
                setConfirmationText('');
              }}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.confirmButton, {
                backgroundColor: color,
                opacity: isResetting ? 0.6 : 1
              }]}
              onPress={onConfirm}
              disabled={isResetting}
            >
              <Text style={styles.confirmButtonText}>
                {isResetting ? 'Resetting...' : 'Reset'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  const themedStyles = {
    container: {
      ...styles.container,
      backgroundColor: currentTheme.colors.background,
    },
    warningContainer: {
      ...styles.warningContainer,
      backgroundColor: currentTheme.colors.warning + '20',
      borderColor: currentTheme.colors.warning + '40',
    },
    warningText: {
      ...styles.warningText,
      color: currentTheme.colors.warning,
    },
    optionsContainer: {
      ...styles.optionsContainer,
      backgroundColor: currentTheme.colors.surface,
    },
    sectionTitle: {
      ...styles.sectionTitle,
      color: currentTheme.colors.text,
    },
    infoContainer: {
      ...styles.infoContainer,
      backgroundColor: currentTheme.colors.surface,
    },
    backupContainer: {
      ...styles.backupContainer,
      backgroundColor: currentTheme.colors.surface,
    },
  };

  return (
    <ScrollView style={themedStyles.container}>
      {/* Warning Header */}
      <View style={themedStyles.warningContainer}>
        <Icon name="warning" size={24} color={currentTheme.colors.warning} />
        <Text style={themedStyles.warningText}>
          Reset options will permanently modify or delete your data. Please proceed with caution.
        </Text>
      </View>

      {/* Reset Options */}
      <View style={themedStyles.optionsContainer}>
        <Text style={themedStyles.sectionTitle}>Reset Options</Text>
        
        {resetOptions.map((option, index) => (
          <View key={index} style={[styles.optionCard, {
            backgroundColor: currentTheme.colors.background,
            borderColor: currentTheme.colors.surface
          }]}>
            <View style={styles.optionHeader}>
              <View style={[styles.optionIcon, { backgroundColor: option.color + '20' }]}>
                <Icon name={option.icon} size={24} color={option.color} />
              </View>
              <View style={styles.optionContent}>
                <Text style={[styles.optionTitle, { color: currentTheme.colors.text }]}>
                  {option.title}
                </Text>
                <Text style={[styles.optionSubtitle, { color: currentTheme.colors.textSecondary }]}>
                  {option.subtitle}
                </Text>
              </View>
            </View>

            <Text style={[styles.optionDescription, { color: currentTheme.colors.textSecondary }]}>
              {option.description}
            </Text>

            <TouchableOpacity
              style={[styles.resetButton, {
                backgroundColor: option.color,
                opacity: isResetting ? 0.6 : 1
              }]}
              onPress={option.onPress}
              disabled={isResetting}
            >
              <Icon name={option.icon} size={20} color="#fff" />
              <Text style={styles.resetButtonText}>{option.title}</Text>
            </TouchableOpacity>
          </View>
        ))}
      </View>

      {/* Data Information */}
      <View style={themedStyles.infoContainer}>
        <Text style={themedStyles.sectionTitle}>Data Information</Text>

        <View style={[styles.infoItem, { borderBottomColor: currentTheme.colors.surface }]}>
          <Icon name="storage" size={20} color={currentTheme.colors.secondary} />
          <View style={styles.infoContent}>
            <Text style={[styles.infoLabel, { color: currentTheme.colors.text }]}>Database Size</Text>
            <Text style={[styles.infoValue, { color: currentTheme.colors.textSecondary }]}>
              {dataStats.dbSize}
            </Text>
          </View>
        </View>

        <View style={[styles.infoItem, { borderBottomColor: currentTheme.colors.surface }]}>
          <Icon name="shopping-cart" size={20} color={currentTheme.colors.success} />
          <View style={styles.infoContent}>
            <Text style={[styles.infoLabel, { color: currentTheme.colors.text }]}>Total Purchases</Text>
            <Text style={[styles.infoValue, { color: currentTheme.colors.textSecondary }]}>
              {dataStats.purchases} records
            </Text>
          </View>
        </View>

        <View style={[styles.infoItem, { borderBottomColor: currentTheme.colors.surface }]}>
          <Icon name="trending-up" size={20} color={currentTheme.colors.accent} />
          <View style={styles.infoContent}>
            <Text style={[styles.infoLabel, { color: currentTheme.colors.text }]}>Usage Records</Text>
            <Text style={[styles.infoValue, { color: currentTheme.colors.textSecondary }]}>
              {dataStats.usage} records
            </Text>
          </View>
        </View>

        <View style={[styles.infoItem, { borderBottomColor: currentTheme.colors.surface }]}>
          <Icon name="history" size={20} color={currentTheme.colors.primary} />
          <View style={styles.infoContent}>
            <Text style={[styles.infoLabel, { color: currentTheme.colors.text }]}>History Entries</Text>
            <Text style={[styles.infoValue, { color: currentTheme.colors.textSecondary }]}>
              {dataStats.history} records
            </Text>
          </View>
        </View>
      </View>

      {/* Backup Information */}
      <View style={themedStyles.backupContainer}>
        <Text style={themedStyles.sectionTitle}>
          <Icon name="backup" size={18} color={currentTheme.colors.secondary} /> Backup Information
        </Text>
        
        <Text style={[styles.backupText, { color: currentTheme.colors.textSecondary }]}>
          Currently, this app stores data locally on your device. Before performing a factory reset, consider:
        </Text>

        <View style={styles.backupList}>
          <Text style={[styles.backupItem, { color: currentTheme.colors.textSecondary }]}>
            • Taking screenshots of important data
          </Text>
          <Text style={[styles.backupItem, { color: currentTheme.colors.textSecondary }]}>
            • Noting down your current settings
          </Text>
          <Text style={[styles.backupItem, { color: currentTheme.colors.textSecondary }]}>
            • Recording your current unit values
          </Text>
          <Text style={[styles.backupItem, { color: currentTheme.colors.textSecondary }]}>
            • Saving your cost per unit configuration
          </Text>
        </View>

        <View style={[styles.futureFeatureNote, { backgroundColor: currentTheme.colors.primary + '10' }]}>
          <Icon name="info" size={16} color={currentTheme.colors.textSecondary} />
          <Text style={[styles.futureFeatureText, { color: currentTheme.colors.textSecondary }]}>
            Cloud backup and restore features will be available in future updates.
          </Text>
        </View>
      </View>

      {/* Modals */}
      {renderResetModal(
        showFactoryResetModal,
        () => setShowFactoryResetModal(false),
        handleFactoryReset,
        'Factory Reset',
        'This will permanently delete ALL your data including purchases, usage records, history, and settings. This action cannot be undone.',
        '#FF3B30'
      )}

      {renderResetModal(
        showDashboardResetModal,
        () => setShowDashboardResetModal(false),
        handleDashboardReset,
        'Dashboard Reset',
        'This will clear your current units, usage calculations, and dashboard totals. Your purchase and usage history will be preserved.',
        '#FF9500'
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  warningContainer: {
    margin: 15,
    padding: 15,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderWidth: 1,
  },
  warningText: {
    flex: 1,
    fontSize: 14,
    marginLeft: 10,
    lineHeight: 20,
  },
  optionsContainer: {
    margin: 15,
    marginTop: 0,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 20,
  },
  optionCard: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 15,
    borderWidth: 1,
  },
  optionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  optionSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  optionDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 15,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  resetButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  infoContainer: {
    margin: 15,
    marginTop: 0,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  infoContent: {
    flex: 1,
    marginLeft: 15,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 14,
    marginTop: 2,
  },
  backupContainer: {
    margin: 15,
    marginTop: 0,
    marginBottom: 30,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backupText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 15,
  },
  backupList: {
    marginBottom: 15,
  },
  backupItem: {
    fontSize: 14,
    lineHeight: 22,
    marginBottom: 4,
  },
  futureFeatureNote: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    borderRadius: 8,
  },
  futureFeatureText: {
    flex: 1,
    fontSize: 12,
    marginLeft: 8,
    fontStyle: 'italic',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 20,
    width: '100%',
    maxWidth: 400,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1D1D1F',
    marginTop: 10,
  },
  modalDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    textAlign: 'center',
    marginBottom: 20,
  },
  confirmationContainer: {
    marginBottom: 20,
  },
  confirmationLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1D1D1F',
    marginBottom: 8,
  },
  confirmationInput: {
    borderWidth: 2,
    borderColor: '#FF3B30',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F2F2F7',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
  },
  confirmButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});

export default ResetOptionsScreen;
