import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { historyService, purchaseService, usageService, totalsService } from '../services/databaseService';
import { HistoryEntry, TotalRecord } from '../types';
import { getAppConfig } from '../storage/asyncStorage';

const HistoryScreen = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'all'>('week');
  const [selectedType, setSelectedType] = useState<'all' | 'purchase' | 'usage'>('all');
  const [historyData, setHistoryData] = useState<HistoryEntry[]>([]);
  const [totalsData, setTotalsData] = useState<any>({
    week: { purchases: { currency: 0, units: 0 }, usage: { units: 0 } },
    month: { purchases: { currency: 0, units: 0 }, usage: { units: 0 } },
  });
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState('USD');

  useEffect(() => {
    loadAppConfig();
    loadHistoryData();
    loadTotalsData();
  }, [selectedPeriod, selectedType]);

  const loadAppConfig = async () => {
    try {
      const config = await getAppConfig();
      setSelectedCurrency(config.selectedCurrency.code);
    } catch (error) {
      console.error('Error loading app config:', error);
    }
  };

  const loadHistoryData = async () => {
    try {
      setIsLoading(true);
      let history: HistoryEntry[] = [];

      if (selectedType === 'all') {
        history = await historyService.getAllHistory();
      } else {
        history = await historyService.getHistoryByType(selectedType);
      }

      // Filter by period
      if (selectedPeriod !== 'all') {
        const now = new Date();
        const startDate = new Date();

        if (selectedPeriod === 'week') {
          startDate.setDate(now.getDate() - 7);
        } else if (selectedPeriod === 'month') {
          startDate.setMonth(now.getMonth() - 1);
        }

        history = history.filter(item =>
          new Date(item.date) >= startDate
        );
      }

      setHistoryData(history);
    } catch (error) {
      console.error('Error loading history:', error);
      Alert.alert('Error', 'Failed to load history data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadTotalsData = async () => {
    try {
      const now = new Date();

      // Calculate weekly totals
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - 7);
      const weekPurchases = await purchaseService.getTotalPurchases(
        weekStart.toISOString(),
        now.toISOString()
      );
      const weekUsage = await usageService.getTotalUsage(
        weekStart.toISOString(),
        now.toISOString()
      );

      // Calculate monthly totals
      const monthStart = new Date(now);
      monthStart.setMonth(now.getMonth() - 1);
      const monthPurchases = await purchaseService.getTotalPurchases(
        monthStart.toISOString(),
        now.toISOString()
      );
      const monthUsage = await usageService.getTotalUsage(
        monthStart.toISOString(),
        now.toISOString()
      );

      setTotalsData({
        week: {
          purchases: weekPurchases,
          usage: { units: weekUsage },
        },
        month: {
          purchases: monthPurchases,
          usage: { units: monthUsage },
        },
      });
    } catch (error) {
      console.error('Error loading totals:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHistoryData();
    await loadTotalsData();
    setRefreshing(false);
  };

  const filteredData = historyData;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderHistoryItem = ({ item }: { item: HistoryEntry }) => (
    <View style={styles.historyItem}>
      <View style={styles.historyHeader}>
        <Icon
          name={item.type === 'purchase' ? 'shopping-cart' : 'trending-up'}
          size={20}
          color={item.type === 'purchase' ? '#34C759' : '#FF9500'}
        />
        <View style={styles.historyInfo}>
          <Text style={styles.historyType}>
            {item.type === 'purchase' ? 'Purchase' : 'Usage'}
          </Text>
          <Text style={styles.historyDate}>{formatDate(item.date)}</Text>
        </View>
        <View style={styles.historyAmount}>
          {item.type === 'purchase' ? (
            <>
              <Text style={styles.currencyAmount}>
                {item.currency_type} {item.currency_value?.toFixed(2)}
              </Text>
              <Text style={styles.unitAmount}>
                {item.amount.toFixed(2)} {item.unit_type}
              </Text>
            </>
          ) : (
            <Text style={styles.usageAmount}>
              {item.amount.toFixed(2)} {item.unit_type}
            </Text>
          )}
        </View>
      </View>
      <Text style={styles.historyDescription}>{item.description}</Text>
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Filter Controls */}
      <View style={styles.filterContainer}>
        <Text style={styles.sectionTitle}>Filter Options</Text>
        
        {/* Period Filter */}
        <View style={styles.filterGroup}>
          <Text style={styles.filterLabel}>Period:</Text>
          <View style={styles.filterButtons}>
            {['week', 'month', 'all'].map((period) => (
              <TouchableOpacity
                key={period}
                style={[
                  styles.filterButton,
                  selectedPeriod === period && styles.activeFilterButton,
                ]}
                onPress={() => setSelectedPeriod(period as any)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    selectedPeriod === period && styles.activeFilterButtonText,
                  ]}
                >
                  {period.charAt(0).toUpperCase() + period.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Type Filter */}
        <View style={styles.filterGroup}>
          <Text style={styles.filterLabel}>Type:</Text>
          <View style={styles.filterButtons}>
            {['all', 'purchase', 'usage'].map((type) => (
              <TouchableOpacity
                key={type}
                style={[
                  styles.filterButton,
                  selectedType === type && styles.activeFilterButton,
                ]}
                onPress={() => setSelectedType(type as any)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    selectedType === type && styles.activeFilterButtonText,
                  ]}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      {/* Totals Summary */}
      <View style={styles.totalsContainer}>
        <Text style={styles.sectionTitle}>
          {selectedPeriod === 'week' ? 'Weekly' : selectedPeriod === 'month' ? 'Monthly' : 'All Time'} Totals
        </Text>
        
        <View style={styles.totalsGrid}>
          <View style={styles.totalCard}>
            <Icon name="shopping-cart" size={24} color="#34C759" />
            <Text style={styles.totalLabel}>Purchases</Text>
            <Text style={styles.totalCurrency}>
              {selectedCurrency} {totalsData[selectedPeriod === 'all' ? 'month' : selectedPeriod]?.purchases.currency.toFixed(2)}
            </Text>
            <Text style={styles.totalUnits}>
              {totalsData[selectedPeriod === 'all' ? 'month' : selectedPeriod]?.purchases.units.toFixed(2)} kWh
            </Text>
          </View>
          
          <View style={styles.totalCard}>
            <Icon name="trending-up" size={24} color="#FF9500" />
            <Text style={styles.totalLabel}>Usage</Text>
            <Text style={styles.totalUsage}>
              {totalsData[selectedPeriod === 'all' ? 'month' : selectedPeriod]?.usage.units.toFixed(2)} kWh
            </Text>
            <Text style={styles.totalRemaining}>
              Remaining: {(totalsData[selectedPeriod === 'all' ? 'month' : selectedPeriod]?.purchases.units - totalsData[selectedPeriod === 'all' ? 'month' : selectedPeriod]?.usage.units).toFixed(2)} kWh
            </Text>
          </View>
        </View>
      </View>

      {/* History List */}
      <View style={styles.historyContainer}>
        <Text style={styles.sectionTitle}>Transaction History</Text>
        {filteredData.length > 0 ? (
          <FlatList
            data={filteredData}
            renderItem={renderHistoryItem}
            keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          />
        ) : (
          <View style={styles.emptyState}>
            <Icon name="history" size={48} color="#C7C7CC" />
            <Text style={styles.emptyStateText}>No history found</Text>
            <Text style={styles.emptyStateSubtext}>
              {selectedType === 'all'
                ? 'Start using the app to see your transaction history'
                : `No ${selectedType} records found for the selected period`
              }
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  filterContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 15,
  },
  filterGroup: {
    marginBottom: 15,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginBottom: 8,
  },
  filterButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F2F2F7',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  activeFilterButton: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
  },
  activeFilterButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  totalsContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  totalsGrid: {
    flexDirection: 'row',
    gap: 15,
  },
  totalCard: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  totalLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginTop: 8,
    marginBottom: 8,
  },
  totalCurrency: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#34C759',
    marginBottom: 4,
  },
  totalUnits: {
    fontSize: 12,
    color: '#666',
  },
  totalUsage: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF9500',
    marginBottom: 4,
  },
  totalRemaining: {
    fontSize: 12,
    color: '#666',
  },
  historyContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    marginBottom: 30,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  historyItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
    paddingVertical: 15,
  },
  historyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  historyInfo: {
    flex: 1,
    marginLeft: 12,
  },
  historyType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
  },
  historyDate: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  historyAmount: {
    alignItems: 'flex-end',
  },
  currencyAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#34C759',
  },
  unitAmount: {
    fontSize: 12,
    color: '#666',
  },
  usageAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF9500',
  },
  historyDescription: {
    fontSize: 14,
    color: '#666',
    marginLeft: 32,
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    backgroundColor: '#F8F9FA',
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E5E5EA',
    borderStyle: 'dashed',
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginTop: 15,
    marginBottom: 5,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default HistoryScreen;
