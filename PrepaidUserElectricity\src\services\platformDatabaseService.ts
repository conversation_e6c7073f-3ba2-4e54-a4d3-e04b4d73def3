import { Platform } from 'react-native';
import { Purchase, UsageRecord, HistoryEntry, TotalRecord, AppSettings } from '../types';

// Import SQLite services for mobile
import { 
  purchaseService as sqlitePurchaseService,
  usageService as sqliteUsageService,
  historyService as sqliteHistoryService,
  settingsService as sqliteSettingsService,
  totalsService as sqliteTotalsService
} from './databaseService';

// Import web storage services
import {
  webPurchaseService,
  webUsageService,
  webHistoryService,
  webSettingsService,
} from '../storage/webStorageAdapter';

// Platform detection
const isWeb = Platform.OS === 'web';

// Purchase operations with platform detection
export const purchaseService = {
  addPurchase: async (purchase: Omit<Purchase, 'id'>): Promise<number> => {
    if (isWeb) {
      return await webPurchaseService.addPurchase(purchase);
    }
    return await sqlitePurchaseService.addPurchase(purchase);
  },

  getAllPurchases: async (): Promise<Purchase[]> => {
    if (isWeb) {
      return await webPurchaseService.getAllPurchases();
    }
    return await sqlitePurchaseService.getAllPurchases();
  },

  getPurchaseById: async (id: number): Promise<Purchase | null> => {
    if (isWeb) {
      return await webPurchaseService.getPurchaseById(id);
    }
    return await sqlitePurchaseService.getPurchaseById(id);
  },

  updatePurchase: async (id: number, updates: Partial<Purchase>): Promise<void> => {
    if (isWeb) {
      return await webPurchaseService.updatePurchase(id, updates);
    }
    return await sqlitePurchaseService.updatePurchase(id, updates);
  },

  deletePurchase: async (id: number): Promise<void> => {
    if (isWeb) {
      return await webPurchaseService.deletePurchase(id);
    }
    return await sqlitePurchaseService.deletePurchase(id);
  },

  getPurchasesByDateRange: async (startDate: string, endDate: string): Promise<Purchase[]> => {
    if (isWeb) {
      return await webPurchaseService.getPurchasesByDateRange(startDate, endDate);
    }
    return await sqlitePurchaseService.getPurchasesByDateRange(startDate, endDate);
  },

  getTotalPurchases: async (startDate: string, endDate: string): Promise<{ currency: number; units: number }> => {
    if (isWeb) {
      return await webPurchaseService.getTotalPurchases(startDate, endDate);
    }
    return await sqlitePurchaseService.getTotalPurchases(startDate, endDate);
  },
};

// Usage operations with platform detection
export const usageService = {
  addUsageRecord: async (usage: Omit<UsageRecord, 'id'>): Promise<number> => {
    if (isWeb) {
      return await webUsageService.addUsageRecord(usage);
    }
    return await sqliteUsageService.addUsageRecord(usage);
  },

  getAllUsageRecords: async (): Promise<UsageRecord[]> => {
    if (isWeb) {
      return await webUsageService.getAllUsageRecords();
    }
    return await sqliteUsageService.getAllUsageRecords();
  },

  getUsageById: async (id: number): Promise<UsageRecord | null> => {
    if (isWeb) {
      return await webUsageService.getUsageById(id);
    }
    return await sqliteUsageService.getUsageById(id);
  },

  updateUsageRecord: async (id: number, updates: Partial<UsageRecord>): Promise<void> => {
    if (isWeb) {
      return await webUsageService.updateUsageRecord(id, updates);
    }
    return await sqliteUsageService.updateUsageRecord(id, updates);
  },

  deleteUsageRecord: async (id: number): Promise<void> => {
    if (isWeb) {
      return await webUsageService.deleteUsageRecord(id);
    }
    return await sqliteUsageService.deleteUsageRecord(id);
  },

  getUsageByDateRange: async (startDate: string, endDate: string): Promise<UsageRecord[]> => {
    if (isWeb) {
      return await webUsageService.getUsageByDateRange(startDate, endDate);
    }
    return await sqliteUsageService.getUsageByDateRange(startDate, endDate);
  },

  getTotalUsage: async (startDate: string, endDate: string): Promise<number> => {
    if (isWeb) {
      return await webUsageService.getTotalUsage(startDate, endDate);
    }
    return await sqliteUsageService.getTotalUsage(startDate, endDate);
  },
};

// History operations with platform detection
export const historyService = {
  addHistoryEntry: async (entry: Omit<HistoryEntry, 'id'>): Promise<number> => {
    if (isWeb) {
      return await webHistoryService.addHistoryEntry(entry);
    }
    return await sqliteHistoryService.addHistoryEntry(entry);
  },

  getAllHistory: async (): Promise<HistoryEntry[]> => {
    if (isWeb) {
      return await webHistoryService.getAllHistory();
    }
    return await sqliteHistoryService.getAllHistory();
  },

  getHistoryByType: async (type: 'purchase' | 'usage'): Promise<HistoryEntry[]> => {
    if (isWeb) {
      return await webHistoryService.getHistoryByType(type);
    }
    return await sqliteHistoryService.getHistoryByType(type);
  },

  getHistoryByDateRange: async (startDate: string, endDate: string): Promise<HistoryEntry[]> => {
    if (isWeb) {
      return await webHistoryService.getHistoryByDateRange(startDate, endDate);
    }
    return await sqliteHistoryService.getHistoryByDateRange(startDate, endDate);
  },

  deleteHistoryEntry: async (id: number): Promise<void> => {
    if (isWeb) {
      return await webHistoryService.deleteHistoryEntry(id);
    }
    return await sqliteHistoryService.deleteHistoryEntry(id);
  },
};

// Settings operations with platform detection
export const settingsService = {
  setSetting: async (key: string, value: string): Promise<void> => {
    if (isWeb) {
      return await webSettingsService.setSetting(key, value);
    }
    return await sqliteSettingsService.setSetting(key, value);
  },

  getSetting: async (key: string, defaultValue?: string): Promise<string | null> => {
    if (isWeb) {
      return await webSettingsService.getSetting(key, defaultValue);
    }
    return await sqliteSettingsService.getSetting(key, defaultValue);
  },

  getAllSettings: async (): Promise<AppSettings[]> => {
    if (isWeb) {
      return await webSettingsService.getAllSettings();
    }
    return await sqliteSettingsService.getAllSettings();
  },

  deleteSetting: async (key: string): Promise<void> => {
    if (isWeb) {
      return await webSettingsService.deleteSetting(key);
    }
    return await sqliteSettingsService.deleteSetting(key);
  },

  clearAllSettings: async (): Promise<void> => {
    if (isWeb) {
      // For web, we'll clear all settings by getting them and deleting each one
      const settings = await webSettingsService.getAllSettings();
      for (const setting of settings) {
        await webSettingsService.deleteSetting(setting.key);
      }
    } else {
      // For SQLite, we need to add this method to the original service
      // For now, we'll implement a simple version
      const settings = await sqliteSettingsService.getAllSettings();
      for (const setting of settings) {
        await sqliteSettingsService.deleteSetting(setting.key);
      }
    }
  },
};

// Totals service - for web, we'll implement a simple version
const webTotalsService = {
  saveTotals: async (total: Omit<TotalRecord, 'id'>): Promise<void> => {
    // For web, we can calculate totals on-the-fly from purchases and usage
    console.log('Web totals saved (calculated on-demand):', total);
  },

  getTotalsByPeriod: async (periodType: 'daily' | 'weekly' | 'monthly'): Promise<TotalRecord[]> => {
    // For web, return empty array - totals will be calculated on-demand
    return [];
  },

  getTotalsByDateRange: async (startDate: string, endDate: string): Promise<TotalRecord[]> => {
    // For web, return empty array - totals will be calculated on-demand
    return [];
  },

  deleteTotals: async (periodType: 'daily' | 'weekly' | 'monthly'): Promise<void> => {
    // For web, no action needed
    console.log('Web totals deletion (no-op):', periodType);
  },
};

export const totalsService = {
  saveTotals: async (total: Omit<TotalRecord, 'id'>): Promise<void> => {
    if (isWeb) {
      return await webTotalsService.saveTotals(total);
    }
    return await sqliteTotalsService.saveTotals(total);
  },

  getTotalsByPeriod: async (periodType: 'daily' | 'weekly' | 'monthly'): Promise<TotalRecord[]> => {
    if (isWeb) {
      return await webTotalsService.getTotalsByPeriod(periodType);
    }
    return await sqliteTotalsService.getTotalsByPeriod(periodType);
  },

  getTotalsByDateRange: async (startDate: string, endDate: string): Promise<TotalRecord[]> => {
    if (isWeb) {
      return await webTotalsService.getTotalsByDateRange(startDate, endDate);
    }
    return await sqliteTotalsService.getTotalsByDateRange(startDate, endDate);
  },

  deleteTotals: async (periodType: 'daily' | 'weekly' | 'monthly'): Promise<void> => {
    if (isWeb) {
      return await webTotalsService.deleteTotals(periodType);
    }
    return await sqliteTotalsService.deleteTotals(periodType);
  },
};

// Platform-aware database initialization
export const initializePlatformDatabase = async (): Promise<void> => {
  if (isWeb) {
    console.log('Web platform detected - using AsyncStorage for data persistence');
    // No initialization needed for web storage
    return;
  } else {
    console.log('Mobile platform detected - initializing SQLite database');
    // Import and initialize SQLite database
    const { initDatabase } = await import('../database/database');
    await initDatabase();
  }
};

// Platform-aware database reset
export const resetPlatformDatabase = async (): Promise<void> => {
  if (isWeb) {
    console.log('Resetting web storage data');
    const { clearAllWebData } = await import('../storage/webStorageAdapter');
    await clearAllWebData();
  } else {
    console.log('Resetting SQLite database');
    const { resetDatabase } = await import('../database/database');
    await resetDatabase();
  }
};
