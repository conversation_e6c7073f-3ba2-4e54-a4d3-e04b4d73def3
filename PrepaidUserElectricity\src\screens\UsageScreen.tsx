import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  FlatList,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { usageService } from '../services/platformDatabaseService';
import { UsageRecord } from '../types';
import { getAppConfig, getCurrentUnits, setCurrentUnits } from '../storage/asyncStorage';
import UsageChart from '../components/UsageChart';
import { notificationService } from '../services/notificationService';

const UsageScreen = () => {
  const [previousUnits, setPreviousUnits] = useState('');
  const [currentUnits, setCurrentUnitsInput] = useState('');
  const [selectedUnit, setSelectedUnit] = useState('kWh');
  const [recentUsage, setRecentUsage] = useState<UsageRecord[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [chartType, setChartType] = useState<'line' | 'area' | 'bar'>('area');
  const [chartPeriod, setChartPeriod] = useState<'week' | 'month'>('week');

  useEffect(() => {
    loadAppConfig();
    loadRecentUsage();
  }, []);

  const loadAppConfig = async () => {
    try {
      const config = await getAppConfig();
      const currentUnitsValue = await getCurrentUnits();
      setSelectedUnit(config.selectedUnit.symbol);
      setPreviousUnits(currentUnitsValue.toString());
    } catch (error) {
      console.error('Error loading app config:', error);
    }
  };

  const loadRecentUsage = async () => {
    try {
      setIsLoading(true);
      const usage = await usageService.getAllUsageRecords();
      setRecentUsage(usage.slice(0, 10)); // Show last 10 usage records
    } catch (error) {
      console.error('Error loading usage records:', error);
      Alert.alert('Error', 'Failed to load recent usage records');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadRecentUsage();
    await loadAppConfig();
    setRefreshing(false);
  };

  // Calculate usage difference
  const calculateUsage = () => {
    const prev = parseFloat(previousUnits) || 0;
    const current = parseFloat(currentUnits) || 0;
    return prev - current;
  };

  const usageDifference = calculateUsage();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return `Today, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffDays === 2) {
      return `Yesterday, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString() + ', ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  const renderUsageItem = ({ item }: { item: UsageRecord }) => (
    <View style={styles.usageItem}>
      <View style={styles.usageHeader}>
        <Icon name="trending-up" size={20} color="#FF9500" />
        <Text style={styles.usageDate}>{formatDate(item.record_date)}</Text>
      </View>
      <View style={styles.usageDetails}>
        <Text style={styles.usageAmount}>
          {item.usage_amount.toFixed(1)} {item.unit_type} used
        </Text>
        <Text style={styles.usageRange}>
          {item.previous_units.toFixed(1)} → {item.current_units.toFixed(1)} {item.unit_type}
        </Text>
      </View>
    </View>
  );

  const handleSaveUsage = async () => {
    if (!currentUnits || parseFloat(currentUnits) < 0) {
      Alert.alert('Error', 'Please enter a valid current unit value');
      return;
    }

    if (usageDifference < 0) {
      Alert.alert('Error', 'Current units cannot be greater than previous units');
      return;
    }

    try {
      setIsLoading(true);

      const usageRecord: Omit<UsageRecord, 'id'> = {
        previous_units: parseFloat(previousUnits),
        current_units: parseFloat(currentUnits),
        usage_amount: usageDifference,
        unit_type: selectedUnit,
        record_date: new Date().toISOString(),
        notes: `Usage recorded: ${usageDifference.toFixed(2)} ${selectedUnit}`,
      };

      await usageService.addUsageRecord(usageRecord);

      // Update current units in storage
      await setCurrentUnits(parseFloat(currentUnits));

      // Send notification
      await notificationService.sendUsageConfirmation(usageDifference, selectedUnit);

      Alert.alert(
        'Usage Recorded',
        `Usage: ${usageDifference.toFixed(2)} ${selectedUnit}`,
        [
          {
            text: 'OK',
            onPress: () => {
              setPreviousUnits(currentUnits);
              setCurrentUnitsInput('');
              loadRecentUsage(); // Refresh the list
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error saving usage:', error);
      Alert.alert('Error', 'Failed to save usage record. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      {/* Usage Input Section */}
      <View style={styles.inputContainer}>
        <Text style={styles.sectionTitle}>Record Current Usage</Text>
        
        {/* Previous Units (Read-only) */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>
            <Icon name="history" size={16} color="#666" /> Previous Units ({selectedUnit})
          </Text>
          <TextInput
            style={[styles.input, styles.readOnlyInput]}
            value={previousUnits}
            editable={false}
            placeholderTextColor="#999"
          />
        </View>

        {/* Current Units Input */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>
            <Icon name="flash-on" size={16} color="#007AFF" /> Current Units ({selectedUnit})
          </Text>
          <TextInput
            style={styles.input}
            value={currentUnits}
            onChangeText={setCurrentUnitsInput}
            placeholder="Enter current meter reading"
            keyboardType="numeric"
            placeholderTextColor="#999"
          />
        </View>

        {/* Usage Calculation Display */}
        <View style={styles.calculationContainer}>
          <Text style={styles.calculationTitle}>
            <Icon name="calculate" size={16} color="#34C759" /> Usage Calculation
          </Text>
          <View style={styles.calculationContent}>
            <View style={styles.calculationRow}>
              <Text style={styles.calculationLabel}>Previous Reading:</Text>
              <Text style={styles.calculationValue}>
                {previousUnits} {selectedUnit}
              </Text>
            </View>
            <View style={styles.calculationRow}>
              <Text style={styles.calculationLabel}>Current Reading:</Text>
              <Text style={styles.calculationValue}>
                {currentUnits || '0'} {selectedUnit}
              </Text>
            </View>
            <View style={[styles.calculationRow, styles.resultRow]}>
              <Text style={styles.resultLabel}>Usage Since Last Recording:</Text>
              <Text style={[styles.resultValue, usageDifference < 0 && styles.errorValue]}>
                {usageDifference.toFixed(2)} {selectedUnit}
              </Text>
            </View>
          </View>
        </View>

        {/* Save Button */}
        <TouchableOpacity 
          style={[styles.saveButton, usageDifference < 0 && styles.disabledButton]} 
          onPress={handleSaveUsage}
          disabled={usageDifference < 0}
        >
          <Icon name="save" size={20} color="#fff" />
          <Text style={styles.saveButtonText}>Record Usage</Text>
        </TouchableOpacity>
      </View>

      {/* Usage Chart */}
      <View style={styles.chartContainer}>
        <View style={styles.chartHeader}>
          <Text style={styles.sectionTitle}>Usage Chart</Text>
          <View style={styles.chartControls}>
            <View style={styles.controlGroup}>
              <Text style={styles.controlLabel}>Period:</Text>
              <View style={styles.controlButtons}>
                {['week', 'month'].map((period) => (
                  <TouchableOpacity
                    key={period}
                    style={[
                      styles.controlButton,
                      chartPeriod === period && styles.activeControlButton,
                    ]}
                    onPress={() => setChartPeriod(period as 'week' | 'month')}
                  >
                    <Text
                      style={[
                        styles.controlButtonText,
                        chartPeriod === period && styles.activeControlButtonText,
                      ]}
                    >
                      {period.charAt(0).toUpperCase() + period.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.controlGroup}>
              <Text style={styles.controlLabel}>Type:</Text>
              <View style={styles.controlButtons}>
                {['line', 'area', 'bar'].map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.controlButton,
                      chartType === type && styles.activeControlButton,
                    ]}
                    onPress={() => setChartType(type as 'line' | 'area' | 'bar')}
                  >
                    <Text
                      style={[
                        styles.controlButtonText,
                        chartType === type && styles.activeControlButtonText,
                      ]}
                    >
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        </View>

        <UsageChart
          data={recentUsage}
          chartType={chartType}
          period={chartPeriod}
          color="#FF9500"
          gradient={['#FF9500', '#FFCC02']}
        />
      </View>

      {/* Recent Usage Records */}
      <View style={styles.recentContainer}>
        <Text style={styles.sectionTitle}>Recent Usage Records</Text>

        {recentUsage.length > 0 ? (
          <FlatList
            data={recentUsage}
            renderItem={renderUsageItem}
            keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          />
        ) : (
          <View style={styles.emptyState}>
            <Icon name="trending-up" size={48} color="#C7C7CC" />
            <Text style={styles.emptyStateText}>No usage records yet</Text>
            <Text style={styles.emptyStateSubtext}>
              Record your first usage above to get started
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  inputContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#007AFF',
    marginBottom: 8,
  },
  input: {
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    backgroundColor: '#F8F9FA',
  },
  readOnlyInput: {
    backgroundColor: '#E5E5EA',
    borderColor: '#C7C7CC',
    color: '#666',
  },
  calculationContainer: {
    backgroundColor: '#F0F9FF',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#34C759',
  },
  calculationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#34C759',
    marginBottom: 10,
  },
  calculationContent: {
    gap: 8,
  },
  calculationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  calculationLabel: {
    fontSize: 14,
    color: '#666',
  },
  calculationValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1D1D1F',
  },
  resultRow: {
    borderTopWidth: 1,
    borderTopColor: '#34C759',
    paddingTop: 8,
    marginTop: 8,
  },
  resultLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#34C759',
  },
  resultValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#34C759',
  },
  errorValue: {
    color: '#FF3B30',
  },
  saveButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#C7C7CC',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  chartContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartHeader: {
    marginBottom: 15,
  },
  chartControls: {
    marginTop: 15,
    gap: 15,
  },
  controlGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  controlLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    flex: 1,
  },
  controlButtons: {
    flexDirection: 'row',
    gap: 8,
    flex: 2,
  },
  controlButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    backgroundColor: '#F2F2F7',
    borderWidth: 1,
    borderColor: '#E5E5EA',
    flex: 1,
    alignItems: 'center',
  },
  activeControlButton: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  controlButtonText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  activeControlButtonText: {
    color: '#fff',
    fontWeight: '600',
  },

  recentContainer: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    marginTop: 0,
    marginBottom: 30,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  usageItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
    paddingVertical: 15,
  },
  usageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  usageDate: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  usageDetails: {
    marginLeft: 28,
  },
  usageAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 4,
  },
  usageRange: {
    fontSize: 14,
    color: '#666',
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    backgroundColor: '#F8F9FA',
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E5E5EA',
    borderStyle: 'dashed',
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginTop: 15,
    marginBottom: 5,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
});

export default UsageScreen;
