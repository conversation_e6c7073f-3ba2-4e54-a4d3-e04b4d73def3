import AsyncStorage from '@react-native-async-storage/async-storage';
import { Purchase, UsageRecord, HistoryEntry, TotalRecord, AppSettings } from '../types';

// Web storage keys for different data types
const WEB_STORAGE_KEYS = {
  PURCHASES: 'web_purchases',
  USAGE_RECORDS: 'web_usage_records',
  HISTORY: 'web_history',
  TOTALS: 'web_totals',
  SETTINGS: 'web_settings',
  COUNTERS: 'web_counters', // For auto-increment IDs
} as const;

// Counter management for auto-increment IDs
let counters = {
  purchases: 0,
  usage_records: 0,
  history: 0,
  totals: 0,
  settings: 0,
};

// Initialize counters from storage
const initializeCounters = async (): Promise<void> => {
  try {
    const storedCounters = await AsyncStorage.getItem(WEB_STORAGE_KEYS.COUNTERS);
    if (storedCounters) {
      counters = JSON.parse(storedCounters);
    }
  } catch (error) {
    console.error('Error initializing counters:', error);
  }
};

// Save counters to storage
const saveCounters = async (): Promise<void> => {
  try {
    await AsyncStorage.setItem(WEB_STORAGE_KEYS.COUNTERS, JSON.stringify(counters));
  } catch (error) {
    console.error('Error saving counters:', error);
  }
};

// Get next ID for a table
const getNextId = async (table: keyof typeof counters): Promise<number> => {
  await initializeCounters();
  counters[table] += 1;
  await saveCounters();
  return counters[table];
};

// Generic storage functions
const storeArray = async <T>(key: string, data: T[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error(`Error storing array for key ${key}:`, error);
    throw error;
  }
};

const getArray = async <T>(key: string): Promise<T[]> => {
  try {
    const data = await AsyncStorage.getItem(key);
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error(`Error getting array for key ${key}:`, error);
    return [];
  }
};

// Purchase operations for web
export const webPurchaseService = {
  addPurchase: async (purchase: Omit<Purchase, 'id'>): Promise<number> => {
    const purchases = await getArray<Purchase>(WEB_STORAGE_KEYS.PURCHASES);
    const id = await getNextId('purchases');
    const newPurchase: Purchase = { ...purchase, id };
    purchases.push(newPurchase);
    await storeArray(WEB_STORAGE_KEYS.PURCHASES, purchases);
    return id;
  },

  getAllPurchases: async (): Promise<Purchase[]> => {
    const purchases = await getArray<Purchase>(WEB_STORAGE_KEYS.PURCHASES);
    return purchases.sort((a, b) => new Date(b.purchase_date).getTime() - new Date(a.purchase_date).getTime());
  },

  getPurchaseById: async (id: number): Promise<Purchase | null> => {
    const purchases = await getArray<Purchase>(WEB_STORAGE_KEYS.PURCHASES);
    return purchases.find(p => p.id === id) || null;
  },

  updatePurchase: async (id: number, updates: Partial<Purchase>): Promise<void> => {
    const purchases = await getArray<Purchase>(WEB_STORAGE_KEYS.PURCHASES);
    const index = purchases.findIndex(p => p.id === id);
    if (index !== -1) {
      purchases[index] = { ...purchases[index], ...updates };
      await storeArray(WEB_STORAGE_KEYS.PURCHASES, purchases);
    }
  },

  deletePurchase: async (id: number): Promise<void> => {
    const purchases = await getArray<Purchase>(WEB_STORAGE_KEYS.PURCHASES);
    const filtered = purchases.filter(p => p.id !== id);
    await storeArray(WEB_STORAGE_KEYS.PURCHASES, filtered);
  },

  getPurchasesByDateRange: async (startDate: string, endDate: string): Promise<Purchase[]> => {
    const purchases = await getArray<Purchase>(WEB_STORAGE_KEYS.PURCHASES);
    return purchases.filter(p => {
      const purchaseDate = new Date(p.purchase_date);
      return purchaseDate >= new Date(startDate) && purchaseDate <= new Date(endDate);
    }).sort((a, b) => new Date(b.purchase_date).getTime() - new Date(a.purchase_date).getTime());
  },

  getTotalPurchases: async (startDate: string, endDate: string): Promise<{ currency: number; units: number }> => {
    const purchases = await webPurchaseService.getPurchasesByDateRange(startDate, endDate);
    return purchases.reduce(
      (totals, purchase) => ({
        currency: totals.currency + purchase.currency_amount,
        units: totals.units + purchase.unit_amount,
      }),
      { currency: 0, units: 0 }
    );
  },
};

// Usage operations for web
export const webUsageService = {
  addUsageRecord: async (usage: Omit<UsageRecord, 'id'>): Promise<number> => {
    const usageRecords = await getArray<UsageRecord>(WEB_STORAGE_KEYS.USAGE_RECORDS);
    const id = await getNextId('usage_records');
    const newUsage: UsageRecord = { ...usage, id };
    usageRecords.push(newUsage);
    await storeArray(WEB_STORAGE_KEYS.USAGE_RECORDS, usageRecords);
    return id;
  },

  getAllUsageRecords: async (): Promise<UsageRecord[]> => {
    const usageRecords = await getArray<UsageRecord>(WEB_STORAGE_KEYS.USAGE_RECORDS);
    return usageRecords.sort((a, b) => new Date(b.record_date).getTime() - new Date(a.record_date).getTime());
  },

  getUsageById: async (id: number): Promise<UsageRecord | null> => {
    const usageRecords = await getArray<UsageRecord>(WEB_STORAGE_KEYS.USAGE_RECORDS);
    return usageRecords.find(u => u.id === id) || null;
  },

  updateUsageRecord: async (id: number, updates: Partial<UsageRecord>): Promise<void> => {
    const usageRecords = await getArray<UsageRecord>(WEB_STORAGE_KEYS.USAGE_RECORDS);
    const index = usageRecords.findIndex(u => u.id === id);
    if (index !== -1) {
      usageRecords[index] = { ...usageRecords[index], ...updates };
      await storeArray(WEB_STORAGE_KEYS.USAGE_RECORDS, usageRecords);
    }
  },

  deleteUsageRecord: async (id: number): Promise<void> => {
    const usageRecords = await getArray<UsageRecord>(WEB_STORAGE_KEYS.USAGE_RECORDS);
    const filtered = usageRecords.filter(u => u.id !== id);
    await storeArray(WEB_STORAGE_KEYS.USAGE_RECORDS, filtered);
  },

  getUsageByDateRange: async (startDate: string, endDate: string): Promise<UsageRecord[]> => {
    const usageRecords = await getArray<UsageRecord>(WEB_STORAGE_KEYS.USAGE_RECORDS);
    return usageRecords.filter(u => {
      const recordDate = new Date(u.record_date);
      return recordDate >= new Date(startDate) && recordDate <= new Date(endDate);
    }).sort((a, b) => new Date(b.record_date).getTime() - new Date(a.record_date).getTime());
  },

  getTotalUsage: async (startDate: string, endDate: string): Promise<number> => {
    const usageRecords = await webUsageService.getUsageByDateRange(startDate, endDate);
    return usageRecords.reduce((total, usage) => total + usage.usage_amount, 0);
  },
};

// History operations for web
export const webHistoryService = {
  addHistoryEntry: async (entry: Omit<HistoryEntry, 'id'>): Promise<number> => {
    const history = await getArray<HistoryEntry>(WEB_STORAGE_KEYS.HISTORY);
    const id = await getNextId('history');
    const newEntry: HistoryEntry = { ...entry, id };
    history.push(newEntry);
    await storeArray(WEB_STORAGE_KEYS.HISTORY, history);
    return id;
  },

  getAllHistory: async (): Promise<HistoryEntry[]> => {
    const history = await getArray<HistoryEntry>(WEB_STORAGE_KEYS.HISTORY);
    return history.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  },

  getHistoryByType: async (type: 'purchase' | 'usage'): Promise<HistoryEntry[]> => {
    const history = await webHistoryService.getAllHistory();
    return history.filter(h => h.type === type);
  },

  getHistoryByDateRange: async (startDate: string, endDate: string): Promise<HistoryEntry[]> => {
    const history = await getArray<HistoryEntry>(WEB_STORAGE_KEYS.HISTORY);
    return history.filter(h => {
      const entryDate = new Date(h.date);
      return entryDate >= new Date(startDate) && entryDate <= new Date(endDate);
    }).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  },

  deleteHistoryEntry: async (id: number): Promise<void> => {
    const history = await getArray<HistoryEntry>(WEB_STORAGE_KEYS.HISTORY);
    const filtered = history.filter(h => h.id !== id);
    await storeArray(WEB_STORAGE_KEYS.HISTORY, filtered);
  },
};

// Settings operations for web
export const webSettingsService = {
  setSetting: async (key: string, value: string): Promise<void> => {
    const settings = await getArray<AppSettings>(WEB_STORAGE_KEYS.SETTINGS);
    const existingIndex = settings.findIndex(s => s.key === key);
    
    if (existingIndex !== -1) {
      settings[existingIndex] = { ...settings[existingIndex], value, updated_at: new Date().toISOString() };
    } else {
      const id = await getNextId('settings');
      const newSetting: AppSettings = {
        id,
        key,
        value,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      settings.push(newSetting);
    }
    
    await storeArray(WEB_STORAGE_KEYS.SETTINGS, settings);
  },

  getSetting: async (key: string, defaultValue?: string): Promise<string | null> => {
    const settings = await getArray<AppSettings>(WEB_STORAGE_KEYS.SETTINGS);
    const setting = settings.find(s => s.key === key);
    return setting?.value || defaultValue || null;
  },

  getAllSettings: async (): Promise<AppSettings[]> => {
    return await getArray<AppSettings>(WEB_STORAGE_KEYS.SETTINGS);
  },

  deleteSetting: async (key: string): Promise<void> => {
    const settings = await getArray<AppSettings>(WEB_STORAGE_KEYS.SETTINGS);
    const filtered = settings.filter(s => s.key !== key);
    await storeArray(WEB_STORAGE_KEYS.SETTINGS, filtered);
  },
};

// Clear all web storage data
export const clearAllWebData = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove([
      WEB_STORAGE_KEYS.PURCHASES,
      WEB_STORAGE_KEYS.USAGE_RECORDS,
      WEB_STORAGE_KEYS.HISTORY,
      WEB_STORAGE_KEYS.TOTALS,
      WEB_STORAGE_KEYS.SETTINGS,
      WEB_STORAGE_KEYS.COUNTERS,
    ]);
    
    // Reset counters
    counters = {
      purchases: 0,
      usage_records: 0,
      history: 0,
      totals: 0,
      settings: 0,
    };
    
    console.log('All web storage data cleared successfully');
  } catch (error) {
    console.error('Error clearing web storage data:', error);
    throw error;
  }
};
