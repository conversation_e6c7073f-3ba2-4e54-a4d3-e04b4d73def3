import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { PurchasesState, Purchase } from '../../types';
import { purchaseService } from '../../services/platformDatabaseService';
import { getAppConfig } from '../../storage/asyncStorage';

// Initial state
const initialState: PurchasesState = {
  purchases: [],
  isLoading: false,
  error: null,
  livePreview: {
    currencyAmount: 0,
    unitAmount: 0,
    costPerUnit: 0,
  },
};

// Async thunks
export const loadPurchases = createAsyncThunk(
  'purchases/loadPurchases',
  async () => {
    try {
      const purchases = await purchaseService.getAllPurchases();
      return purchases;
    } catch (error) {
      console.error('Error loading purchases:', error);
      throw error;
    }
  }
);

export const addPurchase = createAsyncThunk(
  'purchases/addPurchase',
  async (purchase: Omit<Purchase, 'id'>) => {
    try {
      const id = await purchaseService.addPurchase(purchase);
      return { ...purchase, id };
    } catch (error) {
      console.error('Error adding purchase:', error);
      throw error;
    }
  }
);

export const deletePurchase = createAsyncThunk(
  'purchases/deletePurchase',
  async (id: number) => {
    try {
      await purchaseService.deletePurchase(id);
      return id;
    } catch (error) {
      console.error('Error deleting purchase:', error);
      throw error;
    }
  }
);

export const loadPurchasesByDateRange = createAsyncThunk(
  'purchases/loadByDateRange',
  async ({ startDate, endDate }: { startDate: string; endDate: string }) => {
    try {
      const purchases = await purchaseService.getPurchasesByDateRange(startDate, endDate);
      return purchases;
    } catch (error) {
      console.error('Error loading purchases by date range:', error);
      throw error;
    }
  }
);

export const calculateLivePreview = createAsyncThunk(
  'purchases/calculateLivePreview',
  async (currencyAmount: number) => {
    try {
      const config = await getAppConfig();
      const costPerUnit = config.costPerUnit;
      const unitAmount = currencyAmount / costPerUnit;
      
      return {
        currencyAmount,
        unitAmount,
        costPerUnit,
      };
    } catch (error) {
      console.error('Error calculating live preview:', error);
      throw error;
    }
  }
);

// Purchases slice
const purchasesSlice = createSlice({
  name: 'purchases',
  initialState,
  reducers: {
    setLivePreview: (state, action: PayloadAction<{ currencyAmount: number; unitAmount: number; costPerUnit: number }>) => {
      state.livePreview = action.payload;
    },
    clearLivePreview: (state) => {
      state.livePreview = {
        currencyAmount: 0,
        unitAmount: 0,
        costPerUnit: 0,
      };
    },
    clearError: (state) => {
      state.error = null;
    },
    updatePurchase: (state, action: PayloadAction<Purchase>) => {
      const index = state.purchases.findIndex(p => p.id === action.payload.id);
      if (index !== -1) {
        state.purchases[index] = action.payload;
      }
    },
    clearPurchases: (state) => {
      state.purchases = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Load purchases
      .addCase(loadPurchases.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadPurchases.fulfilled, (state, action) => {
        state.isLoading = false;
        state.purchases = action.payload;
        state.error = null;
      })
      .addCase(loadPurchases.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to load purchases';
      })
      
      // Add purchase
      .addCase(addPurchase.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addPurchase.fulfilled, (state, action) => {
        state.isLoading = false;
        state.purchases.unshift(action.payload); // Add to beginning of array
        state.error = null;
      })
      .addCase(addPurchase.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to add purchase';
      })
      
      // Delete purchase
      .addCase(deletePurchase.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deletePurchase.fulfilled, (state, action) => {
        state.isLoading = false;
        state.purchases = state.purchases.filter(p => p.id !== action.payload);
        state.error = null;
      })
      .addCase(deletePurchase.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to delete purchase';
      })
      
      // Load purchases by date range
      .addCase(loadPurchasesByDateRange.fulfilled, (state, action) => {
        state.purchases = action.payload;
      })
      
      // Calculate live preview
      .addCase(calculateLivePreview.fulfilled, (state, action) => {
        state.livePreview = action.payload;
      });
  },
});

export const {
  setLivePreview,
  clearLivePreview,
  clearError,
  updatePurchase,
  clearPurchases,
} = purchasesSlice.actions;

export default purchasesSlice.reducer;
