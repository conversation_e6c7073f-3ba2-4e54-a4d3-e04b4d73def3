import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {
  getAppConfig,
  updateAppConfig,
  DEFAULT_CURRENCIES,
  DEFAULT_UNITS
} from '../../storage/asyncStorage';
import { CurrencyConfig, UnitConfig } from '../../types';
import { settingsService } from '../../services/databaseService';
import { notificationService } from '../../services/notificationService';

const GeneralSettingsScreen = () => {
  const [costPerUnit, setCostPerUnit] = useState('0.15');
  const [thresholdLimit, setThresholdLimit] = useState('20');
  const [selectedCurrency, setSelectedCurrency] = useState('USD');
  const [selectedUnit, setSelectedUnit] = useState('kWh');
  const [customCurrency, setCustomCurrency] = useState('');
  const [customUnit, setCustomUnit] = useState('');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [notificationTime, setNotificationTime] = useState('18:00');
  const [isLoading, setIsLoading] = useState(false);
  const [availableCurrencies, setAvailableCurrencies] = useState<CurrencyConfig[]>(DEFAULT_CURRENCIES);
  const [availableUnits, setAvailableUnits] = useState<UnitConfig[]>(DEFAULT_UNITS);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const config = await getAppConfig();
      setCostPerUnit(config.costPerUnit.toString());
      setThresholdLimit(config.thresholdLimit.toString());
      setSelectedCurrency(config.selectedCurrency.code);
      setSelectedUnit(config.selectedUnit.symbol);
      setNotificationsEnabled(config.notificationSettings.enabled);
      setNotificationTime(config.notificationSettings.time);

      // Load custom currency/unit if they exist
      if (config.selectedCurrency.isCustom) {
        setCustomCurrency(config.selectedCurrency.name);
      }
      if (config.selectedUnit.isCustom) {
        setCustomUnit(config.selectedUnit.name);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      Alert.alert('Error', 'Failed to load settings');
    }
  };

  const currencies = [
    ...DEFAULT_CURRENCIES,
    { name: 'Custom', symbol: '', code: 'CUSTOM', isCustom: true },
  ];

  const units = [
    ...DEFAULT_UNITS,
    { name: 'Custom', symbol: 'CUSTOM', isCustom: true },
  ];

  const handleSaveSettings = async () => {
    // Validation
    if (!costPerUnit || parseFloat(costPerUnit) <= 0) {
      Alert.alert('Error', 'Please enter a valid cost per unit');
      return;
    }

    if (!thresholdLimit || parseFloat(thresholdLimit) < 0) {
      Alert.alert('Error', 'Please enter a valid threshold limit');
      return;
    }

    if (selectedCurrency === 'CUSTOM' && !customCurrency.trim()) {
      Alert.alert('Error', 'Please enter a custom currency name');
      return;
    }

    if (selectedUnit === 'CUSTOM' && !customUnit.trim()) {
      Alert.alert('Error', 'Please enter a custom unit name');
      return;
    }

    try {
      setIsLoading(true);

      // Prepare currency config
      let currencyConfig: CurrencyConfig;
      if (selectedCurrency === 'CUSTOM') {
        currencyConfig = {
          name: customCurrency,
          symbol: customCurrency.substring(0, 3).toUpperCase(),
          code: 'CUSTOM',
          isCustom: true,
        };
      } else {
        currencyConfig = currencies.find(c => c.code === selectedCurrency) || DEFAULT_CURRENCIES[0];
      }

      // Prepare unit config
      let unitConfig: UnitConfig;
      if (selectedUnit === 'CUSTOM') {
        unitConfig = {
          name: customUnit,
          symbol: customUnit,
          isCustom: true,
        };
      } else {
        unitConfig = units.find(u => u.symbol === selectedUnit) || DEFAULT_UNITS[0];
      }

      // Update app config
      await updateAppConfig({
        costPerUnit: parseFloat(costPerUnit),
        thresholdLimit: parseFloat(thresholdLimit),
        selectedCurrency: currencyConfig,
        selectedUnit: unitConfig,
        notificationSettings: {
          enabled: notificationsEnabled,
          time: notificationTime,
          title: 'Electricity Usage Reminder',
          message: 'Don\'t forget to record your electricity usage today!',
        },
      });

      // Save individual settings to database
      await settingsService.setSetting('cost_per_unit', costPerUnit);
      await settingsService.setSetting('threshold_limit', thresholdLimit);
      await settingsService.setSetting('selected_currency', JSON.stringify(currencyConfig));
      await settingsService.setSetting('selected_unit', JSON.stringify(unitConfig));
      await settingsService.setSetting('notifications_enabled', notificationsEnabled.toString());
      await settingsService.setSetting('notification_time', notificationTime);

      // Update notification schedule
      if (notificationsEnabled) {
        await notificationService.scheduleDailyReminder({
          enabled: notificationsEnabled,
          time: notificationTime,
          title: 'Electricity Usage Reminder',
          message: 'Don\'t forget to record your electricity usage today!',
        });
      } else {
        await notificationService.cancelDailyReminder();
      }

      Alert.alert('Success', 'Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      {/* Cost Configuration */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          <Icon name="attach-money" size={18} color="#007AFF" /> Cost Configuration
        </Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Cost Per Unit</Text>
          <TextInput
            style={styles.input}
            value={costPerUnit}
            onChangeText={setCostPerUnit}
            placeholder="0.15"
            keyboardType="numeric"
            placeholderTextColor="#999"
          />
          <Text style={styles.inputHint}>
            Current rate: {selectedCurrency} {costPerUnit}/
            {selectedUnit === 'CUSTOM' ? customUnit : selectedUnit}
          </Text>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Low Units Threshold</Text>
          <TextInput
            style={styles.input}
            value={thresholdLimit}
            onChangeText={setThresholdLimit}
            placeholder="20"
            keyboardType="numeric"
            placeholderTextColor="#999"
          />
          <Text style={styles.inputHint}>
            Warning when units drop below this value
          </Text>
        </View>
      </View>

      {/* Currency Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          <Icon name="monetization-on" size={18} color="#34C759" /> Currency
        </Text>
        
        <View style={styles.optionsGrid}>
          {currencies.map((currency) => (
            <TouchableOpacity
              key={currency.code}
              style={[
                styles.optionButton,
                selectedCurrency === currency.code && styles.selectedOption,
              ]}
              onPress={() => setSelectedCurrency(currency.code)}
            >
              <Text style={styles.optionSymbol}>{currency.symbol || '💱'}</Text>
              <Text
                style={[
                  styles.optionText,
                  selectedCurrency === currency.code && styles.selectedOptionText,
                ]}
              >
                {currency.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {selectedCurrency === 'CUSTOM' && (
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Custom Currency Name</Text>
            <TextInput
              style={styles.input}
              value={customCurrency}
              onChangeText={setCustomCurrency}
              placeholder="Enter currency name"
              placeholderTextColor="#999"
            />
          </View>
        )}
      </View>

      {/* Unit Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          <Icon name="flash-on" size={18} color="#FF9500" /> Units
        </Text>
        
        <View style={styles.optionsGrid}>
          {units.map((unit) => (
            <TouchableOpacity
              key={unit.symbol}
              style={[
                styles.optionButton,
                selectedUnit === unit.symbol && styles.selectedOption,
              ]}
              onPress={() => setSelectedUnit(unit.symbol)}
            >
              <Text style={styles.optionSymbol}>⚡</Text>
              <Text
                style={[
                  styles.optionText,
                  selectedUnit === unit.symbol && styles.selectedOptionText,
                ]}
              >
                {unit.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {selectedUnit === 'CUSTOM' && (
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Custom Unit Name</Text>
            <TextInput
              style={styles.input}
              value={customUnit}
              onChangeText={setCustomUnit}
              placeholder="Enter unit name"
              placeholderTextColor="#999"
            />
          </View>
        )}
      </View>

      {/* Notifications */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          <Icon name="notifications" size={18} color="#5AC8FA" /> Notifications
        </Text>
        
        <View style={styles.switchRow}>
          <View style={styles.switchContent}>
            <Text style={styles.switchLabel}>Daily Usage Reminders</Text>
            <Text style={styles.switchSubtitle}>
              Get reminded to record your usage
            </Text>
          </View>
          <Switch
            value={notificationsEnabled}
            onValueChange={setNotificationsEnabled}
            trackColor={{ false: '#E5E5EA', true: '#007AFF' }}
            thumbColor={notificationsEnabled ? '#fff' : '#fff'}
          />
        </View>

        {notificationsEnabled && (
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Reminder Time</Text>
            <TextInput
              style={styles.input}
              value={notificationTime}
              onChangeText={setNotificationTime}
              placeholder="18:00"
              placeholderTextColor="#999"
            />
            <Text style={styles.inputHint}>
              24-hour format (HH:MM)
            </Text>
          </View>
        )}
      </View>

      {/* Save Button */}
      <View style={styles.saveContainer}>
        <TouchableOpacity
          style={[styles.saveButton, isLoading && styles.disabledButton]}
          onPress={handleSaveSettings}
          disabled={isLoading}
        >
          <Icon name={isLoading ? "hourglass-empty" : "save"} size={20} color="#fff" />
          <Text style={styles.saveButtonText}>
            {isLoading ? 'Saving...' : 'Save Settings'}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  section: {
    backgroundColor: '#FFFFFF',
    margin: 15,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#007AFF',
    marginBottom: 8,
  },
  input: {
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    backgroundColor: '#F8F9FA',
  },
  inputHint: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
    fontStyle: 'italic',
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  optionButton: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#F8F9FA',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E5E5EA',
  },
  selectedOption: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  optionSymbol: {
    fontSize: 24,
    marginBottom: 8,
  },
  optionText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  selectedOptionText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  switchContent: {
    flex: 1,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1D1D1F',
  },
  switchSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  saveContainer: {
    margin: 15,
    marginBottom: 30,
  },
  saveButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    gap: 8,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    backgroundColor: '#C7C7CC',
    opacity: 0.6,
  },
});

export default GeneralSettingsScreen;
