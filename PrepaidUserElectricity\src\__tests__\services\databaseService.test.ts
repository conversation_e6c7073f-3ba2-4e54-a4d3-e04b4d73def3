import { purchaseService, usageService, historyService, settingsService } from '../../services/databaseService';
import { Purchase, UsageRecord } from '../../types';

// Mock the database module
jest.mock('../../database/database', () => ({
  executeQuery: jest.fn(),
  fetchQuery: jest.fn(),
  fetchSingleQuery: jest.fn(),
}));

import { executeQuery, fetchQuery, fetchSingleQuery } from '../../database/database';

const mockExecuteQuery = executeQuery as jest.MockedFunction<typeof executeQuery>;
const mockFetchQuery = fetchQuery as jest.MockedFunction<typeof fetchQuery>;
const mockFetchSingleQuery = fetchSingleQuery as jest.MockedFunction<typeof fetchSingleQuery>;

describe('Database Services', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('purchaseService', () => {
    const mockPurchase: Omit<Purchase, 'id'> = {
      currency_amount: 25.00,
      unit_amount: 166.67,
      currency_type: 'USD',
      unit_type: 'kWh',
      cost_per_unit: 0.15,
      purchase_date: '2024-01-15T14:30:00Z',
      notes: 'Test purchase',
    };

    describe('addPurchase', () => {
      it('should add a purchase successfully', async () => {
        const mockResult = { lastInsertRowId: 1 };
        mockExecuteQuery.mockResolvedValueOnce(mockResult);
        mockExecuteQuery.mockResolvedValueOnce(mockResult); // For history entry

        const result = await purchaseService.addPurchase(mockPurchase);

        expect(result).toBe(1);
        expect(mockExecuteQuery).toHaveBeenCalledTimes(2); // Purchase + history
        expect(mockExecuteQuery).toHaveBeenCalledWith(
          expect.stringContaining('INSERT INTO purchases'),
          expect.arrayContaining([
            mockPurchase.currency_amount,
            mockPurchase.unit_amount,
            mockPurchase.currency_type,
            mockPurchase.unit_type,
            mockPurchase.cost_per_unit,
            mockPurchase.purchase_date,
            mockPurchase.notes,
          ])
        );
      });

      it('should handle database errors', async () => {
        const error = new Error('Database error');
        mockExecuteQuery.mockRejectedValueOnce(error);

        await expect(purchaseService.addPurchase(mockPurchase)).rejects.toThrow('Database error');
      });
    });

    describe('getAllPurchases', () => {
      it('should fetch all purchases', async () => {
        const mockPurchases = [{ ...mockPurchase, id: 1 }];
        mockFetchQuery.mockResolvedValueOnce(mockPurchases);

        const result = await purchaseService.getAllPurchases();

        expect(result).toEqual(mockPurchases);
        expect(mockFetchQuery).toHaveBeenCalledWith(
          'SELECT * FROM purchases ORDER BY purchase_date DESC'
        );
      });
    });

    describe('getTotalPurchases', () => {
      it('should calculate total purchases for a period', async () => {
        const mockTotal = { total_currency: 100, total_units: 666.67 };
        mockFetchSingleQuery.mockResolvedValueOnce(mockTotal);

        const result = await purchaseService.getTotalPurchases('2024-01-01', '2024-01-31');

        expect(result).toEqual({
          currency: 100,
          units: 666.67,
        });
        expect(mockFetchSingleQuery).toHaveBeenCalledWith(
          expect.stringContaining('SUM(currency_amount)'),
          ['2024-01-01', '2024-01-31']
        );
      });

      it('should return zero when no data found', async () => {
        mockFetchSingleQuery.mockResolvedValueOnce(null);

        const result = await purchaseService.getTotalPurchases('2024-01-01', '2024-01-31');

        expect(result).toEqual({
          currency: 0,
          units: 0,
        });
      });
    });
  });

  describe('usageService', () => {
    const mockUsage: Omit<UsageRecord, 'id'> = {
      previous_units: 100,
      current_units: 85,
      usage_amount: 15,
      unit_type: 'kWh',
      record_date: '2024-01-15T18:00:00Z',
      notes: 'Test usage',
    };

    describe('addUsageRecord', () => {
      it('should add a usage record successfully', async () => {
        const mockResult = { lastInsertRowId: 1 };
        mockExecuteQuery.mockResolvedValueOnce(mockResult);
        mockExecuteQuery.mockResolvedValueOnce(mockResult); // For history entry

        const result = await usageService.addUsageRecord(mockUsage);

        expect(result).toBe(1);
        expect(mockExecuteQuery).toHaveBeenCalledTimes(2); // Usage + history
      });
    });

    describe('calculateUsageSinceLastRecording', () => {
      it('should calculate usage since last recording', async () => {
        const mockLatestRecord = { current_units: 90 };
        mockFetchSingleQuery.mockResolvedValueOnce(mockLatestRecord);

        const result = await usageService.calculateUsageSinceLastRecording(85);

        expect(result).toBe(5); // 90 - 85
      });

      it('should return 0 when no previous record exists', async () => {
        mockFetchSingleQuery.mockResolvedValueOnce(null);

        const result = await usageService.calculateUsageSinceLastRecording(85);

        expect(result).toBe(0);
      });
    });
  });

  describe('settingsService', () => {
    describe('setSetting', () => {
      it('should set a setting successfully', async () => {
        mockExecuteQuery.mockResolvedValueOnce({ changes: 1 });

        await settingsService.setSetting('test_key', 'test_value');

        expect(mockExecuteQuery).toHaveBeenCalledWith(
          expect.stringContaining('INSERT OR REPLACE INTO settings'),
          ['test_key', 'test_value']
        );
      });
    });

    describe('getSetting', () => {
      it('should get a setting value', async () => {
        const mockSetting = { value: 'test_value' };
        mockFetchSingleQuery.mockResolvedValueOnce(mockSetting);

        const result = await settingsService.getSetting('test_key');

        expect(result).toBe('test_value');
        expect(mockFetchSingleQuery).toHaveBeenCalledWith(
          'SELECT value FROM settings WHERE key = ?',
          ['test_key']
        );
      });

      it('should return default value when setting not found', async () => {
        mockFetchSingleQuery.mockResolvedValueOnce(null);

        const result = await settingsService.getSetting('test_key', 'default_value');

        expect(result).toBe('default_value');
      });

      it('should return null when no default provided and setting not found', async () => {
        mockFetchSingleQuery.mockResolvedValueOnce(null);

        const result = await settingsService.getSetting('test_key');

        expect(result).toBeNull();
      });
    });
  });

  describe('historyService', () => {
    describe('addHistoryEntry', () => {
      it('should add a history entry successfully', async () => {
        const mockResult = { lastInsertRowId: 1 };
        mockExecuteQuery.mockResolvedValueOnce(mockResult);

        const historyEntry = {
          type: 'purchase' as const,
          reference_id: 1,
          amount: 166.67,
          currency_value: 25.00,
          unit_type: 'kWh',
          currency_type: 'USD',
          date: '2024-01-15T14:30:00Z',
          description: 'Test purchase',
        };

        const result = await historyService.addHistoryEntry(historyEntry);

        expect(result).toBe(1);
        expect(mockExecuteQuery).toHaveBeenCalledWith(
          expect.stringContaining('INSERT INTO history'),
          expect.arrayContaining([
            historyEntry.type,
            historyEntry.reference_id,
            historyEntry.amount,
            historyEntry.currency_value,
            historyEntry.unit_type,
            historyEntry.currency_type,
            historyEntry.date,
            historyEntry.description,
          ])
        );
      });
    });

    describe('getAllHistory', () => {
      it('should fetch all history entries', async () => {
        const mockHistory = [
          {
            id: 1,
            type: 'purchase',
            amount: 166.67,
            date: '2024-01-15T14:30:00Z',
          },
        ];
        mockFetchQuery.mockResolvedValueOnce(mockHistory);

        const result = await historyService.getAllHistory();

        expect(result).toEqual(mockHistory);
        expect(mockFetchQuery).toHaveBeenCalledWith(
          'SELECT * FROM history ORDER BY date DESC'
        );
      });
    });
  });
});
