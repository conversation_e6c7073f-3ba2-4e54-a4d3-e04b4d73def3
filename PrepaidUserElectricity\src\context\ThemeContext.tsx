import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ThemeConfig } from '../types';
import { DEFAULT_THEMES, getSelectedTheme, setSelectedTheme } from '../storage/asyncStorage';

interface ThemeContextType {
  currentTheme: ThemeConfig;
  themes: ThemeConfig[];
  changeTheme: (themeId: string) => Promise<void>;
  isLoading: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState<ThemeConfig>(DEFAULT_THEMES[0]);
  const [themes] = useState<ThemeConfig[]>(DEFAULT_THEMES);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadTheme();
  }, []);

  const loadTheme = async () => {
    try {
      const savedThemeId = await getSelectedTheme();
      const theme = themes.find(t => t.id === savedThemeId) || themes[0];
      setCurrentTheme(theme);
    } catch (error) {
      console.error('Error loading theme:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const changeTheme = async (themeId: string) => {
    try {
      const theme = themes.find(t => t.id === themeId);
      if (theme) {
        setCurrentTheme(theme);
        await setSelectedTheme(themeId);
      }
    } catch (error) {
      console.error('Error changing theme:', error);
      throw error;
    }
  };

  const value: ThemeContextType = {
    currentTheme,
    themes,
    changeTheme,
    isLoading,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Theme utility functions
export const createThemedStyles = (theme: ThemeConfig) => ({
  // Common themed styles that can be reused across components
  container: {
    backgroundColor: theme.colors.background,
  },
  surface: {
    backgroundColor: theme.colors.surface,
  },
  text: {
    color: theme.colors.text,
  },
  textSecondary: {
    color: theme.colors.textSecondary,
  },
  primaryButton: {
    backgroundColor: theme.colors.primary,
  },
  secondaryButton: {
    backgroundColor: theme.colors.secondary,
  },
  accentButton: {
    backgroundColor: theme.colors.accent,
  },
  warningButton: {
    backgroundColor: theme.colors.warning,
  },
  errorButton: {
    backgroundColor: theme.colors.error,
  },
  successButton: {
    backgroundColor: theme.colors.success,
  },
  primaryGradient: {
    colors: theme.gradients.primary,
  },
  secondaryGradient: {
    colors: theme.gradients.secondary,
  },
  accentGradient: {
    colors: theme.gradients.accent,
  },
  card: {
    backgroundColor: theme.colors.surface,
    shadowColor: theme.colors.text,
  },
  border: {
    borderColor: theme.colors.primary,
  },
  input: {
    backgroundColor: theme.colors.surface,
    borderColor: theme.colors.primary,
    color: theme.colors.text,
  },
  placeholder: {
    color: theme.colors.textSecondary,
  },
});

// Hook for getting themed styles
export const useThemedStyles = () => {
  const { currentTheme } = useTheme();
  return createThemedStyles(currentTheme);
};

// Color utility functions
export const getColorWithOpacity = (color: string, opacity: number): string => {
  // Convert hex to rgba
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

export const lightenColor = (color: string, amount: number): string => {
  const hex = color.replace('#', '');
  const r = Math.min(255, parseInt(hex.substring(0, 2), 16) + amount);
  const g = Math.min(255, parseInt(hex.substring(2, 4), 16) + amount);
  const b = Math.min(255, parseInt(hex.substring(4, 6), 16) + amount);
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
};

export const darkenColor = (color: string, amount: number): string => {
  const hex = color.replace('#', '');
  const r = Math.max(0, parseInt(hex.substring(0, 2), 16) - amount);
  const g = Math.max(0, parseInt(hex.substring(2, 4), 16) - amount);
  const b = Math.max(0, parseInt(hex.substring(4, 6), 16) - amount);
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
};

// Gradient utility function
export const createGradientStyle = (colors: string[], direction: 'horizontal' | 'vertical' | 'diagonal' = 'horizontal') => {
  const locations = colors.map((_, index) => index / (colors.length - 1));
  
  let start, end;
  switch (direction) {
    case 'vertical':
      start = { x: 0, y: 0 };
      end = { x: 0, y: 1 };
      break;
    case 'diagonal':
      start = { x: 0, y: 0 };
      end = { x: 1, y: 1 };
      break;
    default: // horizontal
      start = { x: 0, y: 0 };
      end = { x: 1, y: 0 };
  }

  return {
    colors,
    locations,
    start,
    end,
  };
};

// Animation utility functions
export const getThemeTransition = () => ({
  duration: 300,
  useNativeDriver: false,
});

export const createAnimatedStyle = (theme: ThemeConfig, property: string, value: any) => ({
  [property]: value,
  // Add transition properties for smooth theme changes
});

export default ThemeContext;
