import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  View,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme, getColorWithOpacity } from '../context/ThemeContext';

interface ThemedButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'accent' | 'warning' | 'error' | 'success' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  icon?: string;
  iconPosition?: 'left' | 'right';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  gradient?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const ThemedButton: React.FC<ThemedButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  icon,
  iconPosition = 'left',
  disabled = false,
  loading = false,
  fullWidth = false,
  gradient = false,
  style,
  textStyle,
}) => {
  const { currentTheme } = useTheme();
  const { colors, gradients } = currentTheme;

  const getButtonColors = () => {
    switch (variant) {
      case 'primary':
        return {
          background: colors.primary,
          text: '#FFFFFF',
          gradient: gradients.primary,
        };
      case 'secondary':
        return {
          background: colors.secondary,
          text: '#FFFFFF',
          gradient: gradients.secondary,
        };
      case 'accent':
        return {
          background: colors.accent,
          text: '#FFFFFF',
          gradient: gradients.accent,
        };
      case 'warning':
        return {
          background: colors.warning,
          text: '#FFFFFF',
          gradient: [colors.warning, colors.accent],
        };
      case 'error':
        return {
          background: colors.error,
          text: '#FFFFFF',
          gradient: [colors.error, '#FF6B6B'],
        };
      case 'success':
        return {
          background: colors.success,
          text: '#FFFFFF',
          gradient: [colors.success, '#4ECDC4'],
        };
      case 'outline':
        return {
          background: 'transparent',
          text: colors.primary,
          gradient: ['transparent', 'transparent'],
        };
      case 'ghost':
        return {
          background: getColorWithOpacity(colors.primary, 0.1),
          text: colors.primary,
          gradient: [getColorWithOpacity(colors.primary, 0.1), getColorWithOpacity(colors.primary, 0.2)],
        };
      default:
        return {
          background: colors.primary,
          text: '#FFFFFF',
          gradient: gradients.primary,
        };
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingHorizontal: 12,
          paddingVertical: 8,
          fontSize: 14,
          iconSize: 16,
        };
      case 'large':
        return {
          paddingHorizontal: 24,
          paddingVertical: 16,
          fontSize: 18,
          iconSize: 24,
        };
      default: // medium
        return {
          paddingHorizontal: 16,
          paddingVertical: 12,
          fontSize: 16,
          iconSize: 20,
        };
    }
  };

  const buttonColors = getButtonColors();
  const sizeStyles = getSizeStyles();

  const buttonStyle: ViewStyle = {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: sizeStyles.paddingHorizontal,
    paddingVertical: sizeStyles.paddingVertical,
    borderRadius: 12,
    backgroundColor: buttonColors.background,
    opacity: disabled || loading ? 0.6 : 1,
    width: fullWidth ? '100%' : undefined,
    ...(variant === 'outline' && {
      borderWidth: 2,
      borderColor: colors.primary,
    }),
    ...style,
  };

  const textStyleCombined: TextStyle = {
    color: buttonColors.text,
    fontSize: sizeStyles.fontSize,
    fontWeight: '600',
    marginLeft: icon && iconPosition === 'left' ? 8 : 0,
    marginRight: icon && iconPosition === 'right' ? 8 : 0,
    ...textStyle,
  };

  const renderContent = () => (
    <>
      {loading && (
        <ActivityIndicator
          size="small"
          color={buttonColors.text}
          style={{ marginRight: 8 }}
        />
      )}
      {icon && iconPosition === 'left' && !loading && (
        <Icon
          name={icon}
          size={sizeStyles.iconSize}
          color={buttonColors.text}
        />
      )}
      <Text style={textStyleCombined}>{title}</Text>
      {icon && iconPosition === 'right' && !loading && (
        <Icon
          name={icon}
          size={sizeStyles.iconSize}
          color={buttonColors.text}
        />
      )}
    </>
  );

  if (gradient && variant !== 'outline' && variant !== 'ghost') {
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled || loading}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={buttonColors.gradient as [string, string, ...string[]]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={buttonStyle}
        >
          {renderContent()}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Additional styles can be added here if needed
});

export default ThemedButton;
