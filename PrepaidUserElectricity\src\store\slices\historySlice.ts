import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { HistoryState, HistoryEntry, TotalRecord } from '../../types';
import { historyService, totalsService, purchaseService, usageService } from '../../services/databaseService';

// Initial state
const initialState: HistoryState = {
  entries: [],
  totals: [],
  isLoading: false,
  error: null,
  filter: {
    period: 'week',
    type: 'all',
  },
};

// Async thunks
export const loadHistory = createAsyncThunk(
  'history/loadHistory',
  async ({ period, type }: { period: 'week' | 'month' | 'all'; type: 'all' | 'purchase' | 'usage' }) => {
    try {
      let entries: HistoryEntry[] = [];

      // Load entries based on type
      if (type === 'all') {
        entries = await historyService.getAllHistory();
      } else {
        entries = await historyService.getHistoryByType(type);
      }

      // Filter by period
      if (period !== 'all') {
        const now = new Date();
        const startDate = new Date();
        
        if (period === 'week') {
          startDate.setDate(now.getDate() - 7);
        } else if (period === 'month') {
          startDate.setMonth(now.getMonth() - 1);
        }

        entries = entries.filter(entry => 
          new Date(entry.date) >= startDate
        );
      }

      return entries;
    } catch (error) {
      console.error('Error loading history:', error);
      throw error;
    }
  }
);

export const loadTotals = createAsyncThunk(
  'history/loadTotals',
  async ({ period }: { period: 'week' | 'month' | 'all' }) => {
    try {
      const now = new Date();
      let startDate = new Date();
      
      if (period === 'week') {
        startDate.setDate(now.getDate() - 7);
      } else if (period === 'month') {
        startDate.setMonth(now.getMonth() - 1);
      } else {
        // For 'all', get data from the beginning of time
        startDate = new Date(2020, 0, 1); // Arbitrary old date
      }

      // Get purchase totals
      const purchaseTotals = await purchaseService.getTotalPurchases(
        startDate.toISOString(),
        now.toISOString()
      );

      // Get usage totals
      const usageTotals = await usageService.getTotalUsage(
        startDate.toISOString(),
        now.toISOString()
      );

      return {
        period,
        purchases: purchaseTotals,
        usage: { units: usageTotals },
      };
    } catch (error) {
      console.error('Error loading totals:', error);
      throw error;
    }
  }
);

export const clearHistory = createAsyncThunk(
  'history/clearHistory',
  async () => {
    try {
      await historyService.clearHistory();
      return [];
    } catch (error) {
      console.error('Error clearing history:', error);
      throw error;
    }
  }
);

export const refreshHistory = createAsyncThunk(
  'history/refresh',
  async (_, { getState, dispatch }) => {
    const state = getState() as { history: HistoryState };
    const { filter } = state.history;
    
    // Reload history with current filters
    await dispatch(loadHistory(filter));
    await dispatch(loadTotals({ period: filter.period }));
  }
);

// History slice
const historySlice = createSlice({
  name: 'history',
  initialState,
  reducers: {
    setFilter: (state, action: PayloadAction<{ period?: 'week' | 'month' | 'all'; type?: 'all' | 'purchase' | 'usage' }>) => {
      state.filter = { ...state.filter, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
    addHistoryEntry: (state, action: PayloadAction<HistoryEntry>) => {
      state.entries.unshift(action.payload); // Add to beginning of array
    },
    removeHistoryEntry: (state, action: PayloadAction<number>) => {
      state.entries = state.entries.filter(entry => entry.id !== action.payload);
    },
    updateHistoryEntry: (state, action: PayloadAction<HistoryEntry>) => {
      const index = state.entries.findIndex(entry => entry.id === action.payload.id);
      if (index !== -1) {
        state.entries[index] = action.payload;
      }
    },
    clearEntries: (state) => {
      state.entries = [];
    },
    updateTotals: (state, action: PayloadAction<TotalRecord>) => {
      const index = state.totals.findIndex(total => 
        total.period_type === action.payload.period_type &&
        total.period_start === action.payload.period_start
      );
      
      if (index !== -1) {
        state.totals[index] = action.payload;
      } else {
        state.totals.push(action.payload);
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Load history
      .addCase(loadHistory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadHistory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.entries = action.payload;
        state.error = null;
      })
      .addCase(loadHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to load history';
      })
      
      // Load totals
      .addCase(loadTotals.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadTotals.fulfilled, (state, action) => {
        state.isLoading = false;
        // Update or add totals for the specific period
        const { period, purchases, usage } = action.payload;
        
        // For now, we'll store the totals in a simple format
        // In a real app, you might want to store these in the totals array
        state.error = null;
      })
      .addCase(loadTotals.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to load totals';
      })
      
      // Clear history
      .addCase(clearHistory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(clearHistory.fulfilled, (state) => {
        state.isLoading = false;
        state.entries = [];
        state.error = null;
      })
      .addCase(clearHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to clear history';
      });
  },
});

export const {
  setFilter,
  clearError,
  addHistoryEntry,
  removeHistoryEntry,
  updateHistoryEntry,
  clearEntries,
  updateTotals,
} = historySlice.actions;

export default historySlice.reducer;
