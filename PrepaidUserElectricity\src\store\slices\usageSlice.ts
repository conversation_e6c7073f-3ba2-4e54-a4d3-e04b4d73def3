import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { UsageState, UsageRecord } from '../../types';
import { usageService } from '../../services/databaseService';
import { getCurrentUnits, setCurrentUnits } from '../../storage/asyncStorage';

// Initial state
const initialState: UsageState = {
  usageRecords: [],
  isLoading: false,
  error: null,
  currentInput: {
    previousUnits: 0,
    currentUnits: 0,
    calculatedUsage: 0,
  },
};

// Async thunks
export const loadUsageRecords = createAsyncThunk(
  'usage/loadUsageRecords',
  async () => {
    try {
      const records = await usageService.getAllUsageRecords();
      return records;
    } catch (error) {
      console.error('Error loading usage records:', error);
      throw error;
    }
  }
);

export const addUsageRecord = createAsyncThunk(
  'usage/addUsageRecord',
  async (usage: Omit<UsageRecord, 'id'>) => {
    try {
      const id = await usageService.addUsageRecord(usage);
      
      // Update current units in storage
      await setCurrentUnits(usage.current_units);
      
      return { ...usage, id };
    } catch (error) {
      console.error('Error adding usage record:', error);
      throw error;
    }
  }
);

export const deleteUsageRecord = createAsyncThunk(
  'usage/deleteUsageRecord',
  async (id: number) => {
    try {
      await usageService.deleteUsageRecord(id);
      return id;
    } catch (error) {
      console.error('Error deleting usage record:', error);
      throw error;
    }
  }
);

export const loadUsageByDateRange = createAsyncThunk(
  'usage/loadByDateRange',
  async ({ startDate, endDate }: { startDate: string; endDate: string }) => {
    try {
      const records = await usageService.getUsageByDateRange(startDate, endDate);
      return records;
    } catch (error) {
      console.error('Error loading usage by date range:', error);
      throw error;
    }
  }
);

export const calculateUsageInput = createAsyncThunk(
  'usage/calculateInput',
  async ({ previousUnits, currentUnits }: { previousUnits: number; currentUnits: number }) => {
    try {
      const calculatedUsage = previousUnits - currentUnits;
      return {
        previousUnits,
        currentUnits,
        calculatedUsage,
      };
    } catch (error) {
      console.error('Error calculating usage input:', error);
      throw error;
    }
  }
);

export const loadCurrentUnitsForUsage = createAsyncThunk(
  'usage/loadCurrentUnits',
  async () => {
    try {
      const currentUnits = await getCurrentUnits();
      return currentUnits;
    } catch (error) {
      console.error('Error loading current units:', error);
      throw error;
    }
  }
);

// Usage slice
const usageSlice = createSlice({
  name: 'usage',
  initialState,
  reducers: {
    setCurrentInput: (state, action: PayloadAction<{ previousUnits: number; currentUnits: number; calculatedUsage: number }>) => {
      state.currentInput = action.payload;
    },
    updatePreviousUnits: (state, action: PayloadAction<number>) => {
      state.currentInput.previousUnits = action.payload;
      state.currentInput.calculatedUsage = state.currentInput.previousUnits - state.currentInput.currentUnits;
    },
    updateCurrentUnits: (state, action: PayloadAction<number>) => {
      state.currentInput.currentUnits = action.payload;
      state.currentInput.calculatedUsage = state.currentInput.previousUnits - state.currentInput.currentUnits;
    },
    clearCurrentInput: (state) => {
      state.currentInput = {
        previousUnits: 0,
        currentUnits: 0,
        calculatedUsage: 0,
      };
    },
    clearError: (state) => {
      state.error = null;
    },
    updateUsageRecord: (state, action: PayloadAction<UsageRecord>) => {
      const index = state.usageRecords.findIndex(r => r.id === action.payload.id);
      if (index !== -1) {
        state.usageRecords[index] = action.payload;
      }
    },
    clearUsageRecords: (state) => {
      state.usageRecords = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Load usage records
      .addCase(loadUsageRecords.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadUsageRecords.fulfilled, (state, action) => {
        state.isLoading = false;
        state.usageRecords = action.payload;
        state.error = null;
      })
      .addCase(loadUsageRecords.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to load usage records';
      })
      
      // Add usage record
      .addCase(addUsageRecord.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addUsageRecord.fulfilled, (state, action) => {
        state.isLoading = false;
        state.usageRecords.unshift(action.payload); // Add to beginning of array
        state.error = null;
        
        // Update current input with new values
        state.currentInput.previousUnits = action.payload.current_units;
        state.currentInput.currentUnits = 0;
        state.currentInput.calculatedUsage = 0;
      })
      .addCase(addUsageRecord.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to add usage record';
      })
      
      // Delete usage record
      .addCase(deleteUsageRecord.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteUsageRecord.fulfilled, (state, action) => {
        state.isLoading = false;
        state.usageRecords = state.usageRecords.filter(r => r.id !== action.payload);
        state.error = null;
      })
      .addCase(deleteUsageRecord.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to delete usage record';
      })
      
      // Load usage by date range
      .addCase(loadUsageByDateRange.fulfilled, (state, action) => {
        state.usageRecords = action.payload;
      })
      
      // Calculate usage input
      .addCase(calculateUsageInput.fulfilled, (state, action) => {
        state.currentInput = action.payload;
      })
      
      // Load current units
      .addCase(loadCurrentUnitsForUsage.fulfilled, (state, action) => {
        state.currentInput.previousUnits = action.payload;
        state.currentInput.calculatedUsage = state.currentInput.previousUnits - state.currentInput.currentUnits;
      });
  },
});

export const {
  setCurrentInput,
  updatePreviousUnits,
  updateCurrentUnits,
  clearCurrentInput,
  clearError,
  updateUsageRecord,
  clearUsageRecords,
} = usageSlice.actions;

export default usageSlice.reducer;
