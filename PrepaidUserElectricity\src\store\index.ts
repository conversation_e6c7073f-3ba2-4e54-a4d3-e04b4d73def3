import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import dashboardSlice from './slices/dashboardSlice';
import purchasesSlice from './slices/purchasesSlice';
import usageSlice from './slices/usageSlice';
import historySlice from './slices/historySlice';
import settingsSlice from './slices/settingsSlice';

export const store = configureStore({
  reducer: {
    dashboard: dashboardSlice,
    purchases: purchasesSlice,
    usage: usageSlice,
    history: historySlice,
    settings: settingsSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export default store;
