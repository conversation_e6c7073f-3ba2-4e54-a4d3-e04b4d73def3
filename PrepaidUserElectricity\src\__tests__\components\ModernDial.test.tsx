import React from 'react';
import { render } from '@testing-library/react-native';
import ModernDial from '../../components/ModernDial';

// Mock react-native-svg
jest.mock('react-native-svg', () => {
  const React = require('react');
  const { View } = require('react-native');
  
  return {
    Svg: ({ children, ...props }: any) => <View testID="svg" {...props}>{children}</View>,
    Circle: (props: any) => <View testID="circle" {...props} />,
    Defs: ({ children }: any) => <View testID="defs">{children}</View>,
    LinearGradient: ({ children }: any) => <View testID="linear-gradient">{children}</View>,
    Stop: (props: any) => <View testID="stop" {...props} />,
  };
});

describe('ModernDial Component', () => {
  const defaultProps = {
    value: 85.5,
    maxValue: 200,
    size: 200,
    strokeWidth: 20,
    color: '#007AFF',
    backgroundColor: '#E5E5EA',
    label: 'Units Remaining',
    unit: 'kWh',
  };

  it('renders correctly with default props', () => {
    const { getByText, getByTestId } = render(<ModernDial {...defaultProps} />);

    // Check if the value is displayed
    expect(getByText('85.5')).toBeTruthy();
    expect(getByText('kWh')).toBeTruthy();
    expect(getByText('Units Remaining')).toBeTruthy();
    expect(getByText('43%')).toBeTruthy(); // (85.5/200)*100 = 42.75% rounded to 43%

    // Check if SVG elements are rendered
    expect(getByTestId('svg')).toBeTruthy();
  });

  it('calculates percentage correctly', () => {
    const { getByText } = render(
      <ModernDial {...defaultProps} value={100} maxValue={200} />
    );

    expect(getByText('50%')).toBeTruthy(); // (100/200)*100 = 50%
  });

  it('handles zero value correctly', () => {
    const { getByText } = render(
      <ModernDial {...defaultProps} value={0} maxValue={200} />
    );

    expect(getByText('0.0')).toBeTruthy();
    expect(getByText('0%')).toBeTruthy();
  });

  it('handles value greater than maxValue', () => {
    const { getByText } = render(
      <ModernDial {...defaultProps} value={250} maxValue={200} />
    );

    expect(getByText('250.0')).toBeTruthy();
    expect(getByText('100%')).toBeTruthy(); // Should cap at 100%
  });

  it('applies custom colors', () => {
    const customColor = '#FF3B30';
    const { getByTestId } = render(
      <ModernDial {...defaultProps} color={customColor} />
    );

    // The SVG should be rendered (we can't easily test the actual color application in this mock)
    expect(getByTestId('svg')).toBeTruthy();
  });

  it('displays custom label and unit', () => {
    const customLabel = 'Battery Level';
    const customUnit = '%';
    
    const { getByText } = render(
      <ModernDial 
        {...defaultProps} 
        label={customLabel} 
        unit={customUnit}
        value={75}
      />
    );

    expect(getByText(customLabel)).toBeTruthy();
    expect(getByText(customUnit)).toBeTruthy();
    expect(getByText('75.0')).toBeTruthy();
  });

  it('handles different sizes', () => {
    const customSize = 150;
    const { getByTestId } = render(
      <ModernDial {...defaultProps} size={customSize} />
    );

    const container = getByTestId('svg').parent;
    // In a real test environment, you would check the actual style properties
    expect(container).toBeTruthy();
  });

  it('renders with gradient colors', () => {
    const gradient = ['#007AFF', '#5AC8FA'];
    const { getByTestId } = render(
      <ModernDial {...defaultProps} gradient={gradient} />
    );

    expect(getByTestId('linear-gradient')).toBeTruthy();
    expect(getByTestId('stop')).toBeTruthy();
  });

  it('formats decimal values correctly', () => {
    const { getByText } = render(
      <ModernDial {...defaultProps} value={85.567} />
    );

    expect(getByText('85.6')).toBeTruthy(); // Should round to 1 decimal place
  });

  it('handles very small values', () => {
    const { getByText } = render(
      <ModernDial {...defaultProps} value={0.1} maxValue={200} />
    );

    expect(getByText('0.1')).toBeTruthy();
    expect(getByText('0%')).toBeTruthy(); // 0.1/200 = 0.05% rounds to 0%
  });

  it('handles negative values gracefully', () => {
    const { getByText } = render(
      <ModernDial {...defaultProps} value={-10} maxValue={200} />
    );

    expect(getByText('-10.0')).toBeTruthy();
    expect(getByText('0%')).toBeTruthy(); // Percentage should not go below 0%
  });
});
