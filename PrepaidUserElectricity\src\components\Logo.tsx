import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { 
  Path, 
  Defs, 
  LinearGradient, 
  Stop, 
  Circle,
  Polygon 
} from 'react-native-svg';
import { useTheme } from '../context/ThemeContext';

interface LogoProps {
  size?: number;
  animated?: boolean;
  variant?: 'full' | 'icon' | 'minimal';
}

const Logo: React.FC<LogoProps> = ({ 
  size = 60, 
  animated = false, 
  variant = 'full' 
}) => {
  const { currentTheme } = useTheme();
  const { colors, gradients } = currentTheme;

  const renderFullLogo = () => (
    <Svg width={size} height={size} viewBox="0 0 100 100">
      <Defs>
        <LinearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor={gradients.primary[0]} />
          <Stop offset="100%" stopColor={gradients.primary[1]} />
        </LinearGradient>
        <LinearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor={gradients.accent[0]} />
          <Stop offset="100%" stopColor={gradients.accent[1]} />
        </LinearGradient>
      </Defs>
      
      {/* Outer circle background */}
      <Circle
        cx="50"
        cy="50"
        r="45"
        fill="url(#primaryGradient)"
        opacity="0.1"
      />
      
      {/* Main lightning bolt */}
      <Path
        d="M35 25 L55 25 L45 45 L60 45 L40 75 L50 55 L35 55 Z"
        fill="url(#primaryGradient)"
        stroke={colors.surface}
        strokeWidth="2"
      />
      
      {/* Energy particles */}
      <Circle cx="25" cy="35" r="2" fill={colors.accent} opacity="0.8" />
      <Circle cx="75" cy="40" r="1.5" fill={colors.accent} opacity="0.6" />
      <Circle cx="70" cy="65" r="2" fill={colors.accent} opacity="0.8" />
      <Circle cx="30" cy="70" r="1.5" fill={colors.accent} opacity="0.6" />
      
      {/* Power lines */}
      <Path
        d="M20 20 L25 25 M75 20 L80 25 M20 80 L25 75 M75 80 L80 75"
        stroke={colors.secondary}
        strokeWidth="2"
        strokeLinecap="round"
        opacity="0.7"
      />
    </Svg>
  );

  const renderIconLogo = () => (
    <Svg width={size} height={size} viewBox="0 0 100 100">
      <Defs>
        <LinearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor={gradients.primary[0]} />
          <Stop offset="100%" stopColor={gradients.primary[1]} />
        </LinearGradient>
      </Defs>
      
      {/* Simplified lightning bolt */}
      <Path
        d="M30 20 L60 20 L45 50 L70 50 L40 80 L55 50 L30 50 Z"
        fill="url(#iconGradient)"
        stroke={colors.surface}
        strokeWidth="3"
      />
    </Svg>
  );

  const renderMinimalLogo = () => (
    <Svg width={size} height={size} viewBox="0 0 100 100">
      {/* Ultra minimal lightning */}
      <Path
        d="M40 10 L60 40 L50 40 L60 90 L40 60 L50 60 Z"
        fill={colors.primary}
        strokeWidth="0"
      />
    </Svg>
  );

  const renderLogo = () => {
    switch (variant) {
      case 'icon':
        return renderIconLogo();
      case 'minimal':
        return renderMinimalLogo();
      default:
        return renderFullLogo();
    }
  };

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      {renderLogo()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default Logo;
