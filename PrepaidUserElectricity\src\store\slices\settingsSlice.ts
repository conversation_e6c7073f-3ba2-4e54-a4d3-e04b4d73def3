import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { SettingsState, AppConfig, CurrencyConfig, UnitConfig, ThemeConfig, NotificationConfig } from '../../types';
import { 
  getAppConfig, 
  updateAppConfig, 
  DEFAULT_CURRENCIES, 
  DEFAULT_UNITS, 
  DEFAULT_THEMES,
  DEFAULT_FONTS 
} from '../../storage/asyncStorage';
import { settingsService } from '../../services/platformDatabaseService';
import { notificationService } from '../../services/notificationService';

// Initial state
const initialState: SettingsState = {
  config: {
    isFirstLaunch: true,
    currentUnits: 0,
    thresholdLimit: 20,
    costPerUnit: 0.15,
    selectedCurrency: DEFAULT_CURRENCIES[0],
    selectedUnit: DEFAULT_UNITS[0],
    selectedTheme: 'electric_blue',
    selectedFont: 'System',
    notificationSettings: {
      enabled: true,
      time: '18:00',
      title: 'Electricity Usage Reminder',
      message: 'Don\'t forget to record your electricity usage today!',
    },
    lastUsageDate: new Date().toISOString(),
  },
  availableCurrencies: DEFAULT_CURRENCIES,
  availableUnits: DEFAULT_UNITS,
  availableThemes: DEFAULT_THEMES,
  availableFonts: DEFAULT_FONTS,
  isLoading: false,
  error: null,
};

// Async thunks
export const loadSettings = createAsyncThunk(
  'settings/loadSettings',
  async () => {
    try {
      const config = await getAppConfig();
      return config;
    } catch (error) {
      console.error('Error loading settings:', error);
      throw error;
    }
  }
);

export const saveSettings = createAsyncThunk(
  'settings/saveSettings',
  async (updates: Partial<AppConfig>) => {
    try {
      const updatedConfig = await updateAppConfig(updates);
      
      // Save individual settings to database
      if (updates.costPerUnit !== undefined) {
        await settingsService.setSetting('cost_per_unit', updates.costPerUnit.toString());
      }
      if (updates.thresholdLimit !== undefined) {
        await settingsService.setSetting('threshold_limit', updates.thresholdLimit.toString());
      }
      if (updates.selectedCurrency) {
        await settingsService.setSetting('selected_currency', JSON.stringify(updates.selectedCurrency));
      }
      if (updates.selectedUnit) {
        await settingsService.setSetting('selected_unit', JSON.stringify(updates.selectedUnit));
      }
      if (updates.selectedTheme) {
        await settingsService.setSetting('selected_theme', updates.selectedTheme);
      }
      if (updates.selectedFont) {
        await settingsService.setSetting('selected_font', updates.selectedFont);
      }
      if (updates.notificationSettings) {
        await settingsService.setSetting('notification_settings', JSON.stringify(updates.notificationSettings));
        
        // Update notification schedule
        if (updates.notificationSettings.enabled) {
          await notificationService.scheduleDailyReminder(updates.notificationSettings);
        } else {
          await notificationService.cancelDailyReminder();
        }
      }
      
      return updatedConfig;
    } catch (error) {
      console.error('Error saving settings:', error);
      throw error;
    }
  }
);

export const resetSettings = createAsyncThunk(
  'settings/resetSettings',
  async (type: 'all' | 'dashboard' | 'appearance') => {
    try {
      let updates: Partial<AppConfig> = {};
      
      switch (type) {
        case 'all':
          // Reset everything except first launch flag
          updates = {
            currentUnits: 0,
            thresholdLimit: 20,
            costPerUnit: 0.15,
            selectedCurrency: DEFAULT_CURRENCIES[0],
            selectedUnit: DEFAULT_UNITS[0],
            selectedTheme: 'electric_blue',
            selectedFont: 'System',
            notificationSettings: {
              enabled: true,
              time: '18:00',
              title: 'Electricity Usage Reminder',
              message: 'Don\'t forget to record your electricity usage today!',
            },
            lastUsageDate: new Date().toISOString(),
          };
          break;
          
        case 'dashboard':
          updates = {
            currentUnits: 0,
            lastUsageDate: new Date().toISOString(),
          };
          break;
          
        case 'appearance':
          updates = {
            selectedTheme: 'electric_blue',
            selectedFont: 'System',
          };
          break;
      }
      
      const updatedConfig = await updateAppConfig(updates);
      
      // Clear relevant database settings
      if (type === 'all') {
        await settingsService.clearAllSettings();
        await notificationService.cancelAllNotifications();
      }
      
      return updatedConfig;
    } catch (error) {
      console.error('Error resetting settings:', error);
      throw error;
    }
  }
);

export const addCustomCurrency = createAsyncThunk(
  'settings/addCustomCurrency',
  async (currency: CurrencyConfig) => {
    try {
      // Save custom currency to database
      await settingsService.setSetting(`custom_currency_${currency.code}`, JSON.stringify(currency));
      return currency;
    } catch (error) {
      console.error('Error adding custom currency:', error);
      throw error;
    }
  }
);

export const addCustomUnit = createAsyncThunk(
  'settings/addCustomUnit',
  async (unit: UnitConfig) => {
    try {
      // Save custom unit to database
      await settingsService.setSetting(`custom_unit_${unit.symbol}`, JSON.stringify(unit));
      return unit;
    } catch (error) {
      console.error('Error adding custom unit:', error);
      throw error;
    }
  }
);

// Settings slice
const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    updateConfig: (state, action: PayloadAction<Partial<AppConfig>>) => {
      state.config = { ...state.config, ...action.payload };
    },
    setCostPerUnit: (state, action: PayloadAction<number>) => {
      state.config.costPerUnit = action.payload;
    },
    setThresholdLimit: (state, action: PayloadAction<number>) => {
      state.config.thresholdLimit = action.payload;
    },
    setCurrentUnits: (state, action: PayloadAction<number>) => {
      state.config.currentUnits = action.payload;
    },
    setSelectedCurrency: (state, action: PayloadAction<CurrencyConfig>) => {
      state.config.selectedCurrency = action.payload;
    },
    setSelectedUnit: (state, action: PayloadAction<UnitConfig>) => {
      state.config.selectedUnit = action.payload;
    },
    setSelectedTheme: (state, action: PayloadAction<string>) => {
      state.config.selectedTheme = action.payload;
    },
    setSelectedFont: (state, action: PayloadAction<string>) => {
      state.config.selectedFont = action.payload;
    },
    setNotificationSettings: (state, action: PayloadAction<NotificationConfig>) => {
      state.config.notificationSettings = action.payload;
    },
    setFirstLaunchComplete: (state) => {
      state.config.isFirstLaunch = false;
    },
    clearError: (state) => {
      state.error = null;
    },
    addAvailableCurrency: (state, action: PayloadAction<CurrencyConfig>) => {
      if (!state.availableCurrencies.find(c => c.code === action.payload.code)) {
        state.availableCurrencies.push(action.payload);
      }
    },
    addAvailableUnit: (state, action: PayloadAction<UnitConfig>) => {
      if (!state.availableUnits.find(u => u.symbol === action.payload.symbol)) {
        state.availableUnits.push(action.payload);
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Load settings
      .addCase(loadSettings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.config = action.payload;
        state.error = null;
      })
      .addCase(loadSettings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to load settings';
      })
      
      // Save settings
      .addCase(saveSettings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(saveSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.config = action.payload;
        state.error = null;
      })
      .addCase(saveSettings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to save settings';
      })
      
      // Reset settings
      .addCase(resetSettings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resetSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.config = action.payload;
        state.error = null;
      })
      .addCase(resetSettings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to reset settings';
      })
      
      // Add custom currency
      .addCase(addCustomCurrency.fulfilled, (state, action) => {
        if (!state.availableCurrencies.find(c => c.code === action.payload.code)) {
          state.availableCurrencies.push(action.payload);
        }
      })
      
      // Add custom unit
      .addCase(addCustomUnit.fulfilled, (state, action) => {
        if (!state.availableUnits.find(u => u.symbol === action.payload.symbol)) {
          state.availableUnits.push(action.payload);
        }
      });
  },
});

export const {
  updateConfig,
  setCostPerUnit,
  setThresholdLimit,
  setCurrentUnits,
  setSelectedCurrency,
  setSelectedUnit,
  setSelectedTheme,
  setSelectedFont,
  setNotificationSettings,
  setFirstLaunchComplete,
  clearError,
  addAvailableCurrency,
  addAvailableUnit,
} = settingsSlice.actions;

export default settingsSlice.reducer;
