import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { View, StyleSheet } from 'react-native';
import AppNavigator from './src/navigation/AppNavigator';
import { initDatabase } from './src/database/database';
import { isFirstLaunch } from './src/storage/asyncStorage';
import { ThemeProvider } from './src/context/ThemeContext';
import { notificationService } from './src/services/notificationService';
import { Provider } from 'react-redux';
import { store } from './src/store';

export default function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [showInitialSetup, setShowInitialSetup] = useState(false);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Initialize database
      await initDatabase();

      // Initialize notification service
      await notificationService.initialize();

      // Check if this is first launch
      const firstLaunch = await isFirstLaunch();
      setShowInitialSetup(firstLaunch);

      setIsLoading(false);
    } catch (error) {
      console.error('Error initializing app:', error);
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        {/* TODO: Add loading spinner */}
      </View>
    );
  }

  return (
    <Provider store={store}>
      <ThemeProvider>
        <View style={styles.container}>
          <StatusBar style="auto" />
          <AppNavigator />
        </View>
      </ThemeProvider>
    </Provider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: '#F2F2F7',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
